PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: /usr/lib/php/20230831/openssl (/usr/lib/php/20230831/openssl: cannot open shared object file: No such file or directory), /usr/lib/php/20230831/openssl.so (/usr/lib/php/20230831/openssl.so: cannot open shared object file: No such file or directory)) in Unknown on line 0
PHP Warning:  PHP Startup: Unable to load dynamic library 'pdo_pgsql' (tried: /usr/lib/php/20230831/pdo_pgsql (/usr/lib/php/20230831/pdo_pgsql: cannot open shared object file: No such file or directory), /usr/lib/php/20230831/pdo_pgsql.so (/usr/lib/php/20230831/pdo_pgsql.so: undefined symbol: pdo_parse_params)) in Unknown on line 0
PHP Warning:  Module "pgsql" is already loaded in Unknown on line 0
[Tue Aug 05 00:00:02.250570 2025] [ssl:warn] [pid 2096] AH01909: amiworryfree.com:80:0 server certificate does NOT include an ID which matches the server name
[Tue Aug 05 00:00:02.250916 2025] [ssl:warn] [pid 2096] AH01909: www.wadenains.com:80:0 server certificate does NOT include an ID which matches the server name
[Tue Aug 05 00:00:02.251456 2025] [mpm_prefork:notice] [pid 2096] AH00163: Apache/2.4.41 (Ubuntu) OpenSSL/1.1.1f mod_perl/2.0.11 Perl/v5.30.0   configured -- resuming normal operations
[Tue Aug 05 00:00:02.251458 2025] [core:notice] [pid 2096] AH00094: Command line: '/usr/sbin/apache2'
