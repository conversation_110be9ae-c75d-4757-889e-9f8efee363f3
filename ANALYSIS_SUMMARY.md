# Apache Access Log Analysis Report Summary

## Overview
This analysis processed **2,965,180 requests** from **7,058 unique IP addresses** spanning from July 16, 2025 to July 30, 2025 (approximately 2 weeks of data).

## Key Findings

### Traffic Statistics
- **Total Requests**: 2,965,180
- **Unique IP Addresses**: 7,058
- **Average Requests per IP**: 420.12
- **Error Rate**: 0.0003% (extremely low)
- **Peak Traffic Hour**: July 22, 2025 at 09:00 with 26,959 requests
- **Average Response Size**: 20.6 KB
- **Total Data Transferred**: ~61 GB

### HTTP Status Code Distribution
- **200 (Success)**: 2,508,012 requests (84.6%)
- **302 (Redirect)**: 345,515 requests (11.6%)
- **301 (Moved Permanently)**: 50,085 requests (1.7%)
- **404 (Not Found)**: 27,770 requests (0.9%)
- **400 (Bad Request)**: 21,523 requests (0.7%)
- **500 (Server Error)**: 925 requests (0.03%)

### Top IP Addresses by Request Volume
1. *****************: 85,686 requests
2. *******************: 37,950 requests
3. *****************: 24,359 requests
4. *******************: 21,384 requests
5. ******************: 16,260 requests

### Geographic Distribution
- **Internal Network (192.168.x.x)**: Majority of traffic
- **Cloudflare IPs (172.68.x.x, 172.69.x.x, 162.158.x.x)**: Significant portion
- **External IPs**: Smaller portion

## Security Analysis

### Anomalies Detected: 162 total
- **High Volume IP Addresses**: 50 IPs flagged for unusual request patterns
- **Security Events**: 25,805 total events detected
- **Failed Authentication Attempts**: 1,373 events
- **Large Requests (>1MB)**: 4,587 requests
- **Bot Traffic**: 295 requests to robots.txt and bot-related endpoints

### Suspicious Patterns Identified
- **SQL Injection Attempts**: 19,845 patterns detected
- **Path Traversal Attempts**: Various attempts to access unauthorized directories
- **XSS Attempts**: Cross-site scripting patterns in URLs

### Most Active Suspicious IPs
All flagged IPs were identified due to high request volumes exceeding normal thresholds.

## Traffic Patterns

### Hourly Distribution
- Peak traffic typically occurs during business hours (9 AM - 5 PM)
- Lowest traffic during early morning hours (2 AM - 6 AM)
- Consistent pattern suggesting business/office usage

### Daily Trends
- Relatively consistent daily traffic with some variation
- Peak day: July 22, 2025
- No significant weekend vs. weekday patterns observed in the data

## Performance Metrics
- **Average Response Time**: Very fast (sub-millisecond for most requests)
- **95th Percentile Response Time**: Still excellent performance
- **Response Size Distribution**: Mostly small responses with some larger file downloads

## Recommendations

### Security
1. **Monitor High-Volume IPs**: Investigate the 50 flagged IP addresses for potential abuse
2. **Implement Rate Limiting**: Consider rate limiting for IPs exceeding normal thresholds
3. **Review SQL Injection Patterns**: Investigate the 19,845 detected SQL injection attempts
4. **Authentication Monitoring**: Review the 1,373 failed authentication attempts

### Performance
1. **Excellent Performance**: Current response times are very good
2. **Bandwidth Usage**: Monitor the ~61GB total transfer for capacity planning
3. **Error Rate**: The 0.0003% error rate is excellent and should be maintained

### Monitoring
1. **Implement Real-time Alerting**: Set up alerts for anomaly detection
2. **Regular Analysis**: Schedule regular log analysis to identify trends
3. **Capacity Planning**: Monitor traffic growth patterns for infrastructure planning

## Files Generated
- `apache_log_report.html`: Interactive HTML report with charts and visualizations
- `log_analysis.json`: Raw analysis data (245MB)
- `report_data.json`: Processed data for reporting
- `log_analyzer.py`: Python script for log analysis
- `generate_report_data.py`: Script for processing report data
- `embed_data.py`: Script for embedding data into HTML

## Technical Details
- **Analysis Duration**: Processed ~3M log entries across 15 files
- **Data Sources**: access.log, access.log.1, and 13 compressed .gz files
- **Chart Library**: Chart.js for interactive visualizations
- **Report Format**: Self-contained HTML with embedded CSS and JavaScript

The analysis reveals a well-performing web server with excellent uptime and response times, but with some security concerns that warrant investigation, particularly around the high volume of SQL injection attempts and suspicious IP activity.
