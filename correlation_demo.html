
<!DOCTYPE html>
<html>
<head>
    <title>Log Correlation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { display: flex; justify-content: space-around; margin: 20px 0; }
        .stat-box { background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }
        .correlation { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .correlation.high-score { border-left: 5px solid #28a745; }
        .correlation.medium-score { border-left: 5px solid #ffc107; }
        .correlation.low-score { border-left: 5px solid #dc3545; }
        .error-details { background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .timestamp { color: #666; font-size: 0.9em; }
        .url { font-family: monospace; background-color: #f1f1f1; padding: 2px 4px; }
        .error-message { font-family: monospace; font-size: 0.9em; color: #d63384; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Log Correlation Report</h1>
        <p>Generated on: 2025-08-05 07:51:02</p>
    </div>
    
    <div class="summary">
        <div class="stat-box">
            <h3>1</h3>
            <p>Total 500 Errors</p>
        </div>
        <div class="stat-box">
            <h3>1</h3>
            <p>Correlated Errors</p>
        </div>
        <div class="stat-box">
            <h3>100.0%</h3>
            <p>Correlation Rate</p>
        </div>
        <div class="stat-box">
            <h3>79.3</h3>
            <p>Avg Score</p>
        </div>
    </div>
    
    <h2>Correlated Errors</h2>

    <div class="correlation medium-score">
        <h3>Correlation #1 (Score: 79.3)</h3>
        <div class="timestamp">Access Log: 2025-08-05T00:38:41</div>
        <p><strong>IP:</strong> *************</p>
        <p><strong>URL:</strong> <span class="url">/imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926</span></p>
        <p><strong>Method:</strong> GET</p>
        <p><strong>Response Time:</strong> 63ms</p>
        <p><strong>Correlation Method:</strong> time_match(0.0s)+ip_match</p>
        
        <h4>Related Error Log Entries (15)</h4>

        <div class="error-details">
            <div class="timestamp">Error #1: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:40 2025] client_sync_common.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.003362 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] client_sync_common.pm: "my" variable $modified_date masks earlier declaration in same scope at /imtonline/Common/Platform/client_sync_common.pm line 885.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #2: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:40 2025] client_sync_common.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.003405 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] client_sync_common.pm: "my" variable $version_type masks earlier declaration in same scope at /imtonline/Common/Platform/client_sync_common.pm line 887.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #3: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:40 2025] client_sync_common.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.003430 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] client_sync_common.pm: "my" variable $status masks earlier declaration in same scope at /imtonline/Common/Platform/client_sync_common.pm line 887.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #4: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] ClientEngine.pl</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.036447 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] ClientEngine.pl: Client::ClientEngine::decode_json() called too early to check prototype at /imtonline/Client/ClientEngine.pl line 95.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #5: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] ClientEngine.pl</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.041563 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] ClientEngine.pl: Use of uninitialized value in string ne at /imtonline/Client/ClientEngine.pl line 93.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #6: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] ClientEngine.pl</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.042601 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] ClientEngine.pl: Use of uninitialized value in string eq at /imtonline/Client/ClientEngine.pl line 162.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #7: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] Connect.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.717274 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::carp redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #8: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] Connect.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.717314 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::confess redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #9: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] Connect.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.717334 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::croak redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #10: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] usersCommon.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.835781 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::carp redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #11: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] usersCommon.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.835829 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::croak redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #12: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] usersCommon.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.835853 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::confess redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #13: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] Maintenance.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.844989 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Maintenance.pm: Useless use of concatenation (.) or string in void context at /imtonline/Common/Maintenance.pm line 637.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #14: 2025-08-05T00:38:41</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:41 2025] Authenticate.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:41.901227 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Authenticate.pm: Scalar value @foundType[0] better written as $foundType[0] at /imtonline/Common/Authenticate.pm line 157.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq</pre>
            </details>
        </div>

        <div class="error-details">
            <div class="timestamp">Error #15: 2025-08-05T00:38:40</div>
            <p><strong>Level:</strong> cgi</p>
            <p><strong>PID:</strong> 636072</p>
            <p><strong>Script:</strong> https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">[Tue Aug  5 00:38:40 2025] MAJ_Account.pm</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>[Tue Aug 05 00:38:40.943406 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] MAJ_Account.pm: Smartmatch is experimental at /imtonline/Common/GAS/MAJ_Account.pm line 233.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303</pre>
            </details>
        </div>

    </div>

</body>
</html>
