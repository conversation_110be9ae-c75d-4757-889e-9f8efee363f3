<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apache Access Log Analysis Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .section-content {
            padding: 1.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-card p {
            opacity: 0.9;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 2rem;
        }
        
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-high {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-medium {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-low {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-danger {
            color: #fff;
            background-color: #dc3545;
        }
        
        .badge-warning {
            color: #212529;
            background-color: #ffc107;
        }
        
        .badge-info {
            color: #fff;
            background-color: #17a2b8;
        }

        .progress {
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .error-detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .error-detail-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
        }

        .error-detail-item strong {
            display: block;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .error-detail-item code {
            background: #e9ecef;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }

        .clickable-row {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .clickable-row:hover {
            background-color: #f8f9fa !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .chart-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Apache Access Log Analysis Report</h1>
            <p>Comprehensive analysis of web server traffic and security patterns</p>
            <p id="report-date"></p>
        </div>

        <!-- Executive Summary -->
        <div class="section">
            <div class="section-header">
                <h2>📊 Executive Summary</h2>
            </div>
            <div class="section-content">
                <div class="stats-grid" id="summary-stats">
                    <!-- Stats will be populated by JavaScript -->
                </div>
                <div id="summary-text">
                    <!-- Summary text will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Traffic Overview -->
        <div class="section">
            <div class="section-header">
                <h2>🚦 Traffic Overview</h2>
            </div>
            <div class="section-content">
                <div class="chart-grid">
                    <div>
                        <h3>Daily Traffic Pattern</h3>
                        <div class="chart-container">
                            <canvas id="dailyTrafficChart"></canvas>
                        </div>
                    </div>
                    <div>
                        <h3>Hourly Traffic Pattern</h3>
                        <div class="chart-container">
                            <canvas id="hourlyTrafficChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Response Code Analysis -->
        <div class="section">
            <div class="section-header">
                <h2>📈 Response Code Analysis</h2>
            </div>
            <div class="section-content">
                <div class="chart-grid">
                    <div>
                        <h3>HTTP Status Code Distribution</h3>
                        <div class="chart-container">
                            <canvas id="statusCodeChart"></canvas>
                        </div>
                    </div>
                    <div>
                        <h3>Geographic Distribution</h3>
                        <div class="chart-container">
                            <canvas id="geoChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Resources -->
        <div class="section">
            <div class="section-header">
                <h2>🔝 Top Resources</h2>
            </div>
            <div class="section-content">
                <div class="chart-grid">
                    <div>
                        <h3>Top 10 IP Addresses</h3>
                        <div class="chart-container">
                            <canvas id="topIPsChart"></canvas>
                        </div>
                    </div>
                    <div>
                        <h3>Top 10 Requested URLs</h3>
                        <div class="chart-container">
                            <canvas id="topURLsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 500 Error Analysis -->
        <div class="section">
            <div class="section-header">
                <h2>🔥 500 Error Analysis</h2>
            </div>
            <div class="section-content">
                <div class="stats-grid" id="error-500-stats">
                    <!-- 500 error stats will be populated by JavaScript -->
                </div>
                <div class="chart-grid">
                    <div>
                        <h3>Top URLs with 500 Errors</h3>
                        <div class="chart-container">
                            <canvas id="top500URLsChart"></canvas>
                        </div>
                    </div>
                    <div>
                        <h3>Top IPs Generating 500 Errors</h3>
                        <div class="chart-container">
                            <canvas id="top500IPsChart"></canvas>
                        </div>
                    </div>
                </div>
                <div id="recent-500-errors">
                    <!-- Recent 500 errors table will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Security Alerts -->
        <div class="section">
            <div class="section-header">
                <h2>🚨 Security Alerts & Anomalies</h2>
            </div>
            <div class="section-content" id="security-alerts">
                <!-- Security alerts will be populated by JavaScript -->
            </div>
        </div>

        <!-- Detailed Findings -->
        <div class="section">
            <div class="section-header">
                <h2>🔍 Detailed Findings</h2>
            </div>
            <div class="section-content" id="detailed-findings">
                <!-- Detailed findings will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Error Detail Modal -->
    <div id="errorModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🔥 500 Error Details</h2>
                <button class="close" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="modal-body" id="errorModalBody">
                <!-- Error details will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // This will be populated with actual data
        let reportData = {};
        
        // Load report data and initialize
        async function loadReportData() {
            try {
                const response = await fetch('report_data.json');
                reportData = await response.json();
                initializeReport();
            } catch (error) {
                console.error('Error loading report data:', error);
                // Fallback to embedded data if fetch fails
                loadEmbeddedData();
            }
        }
        
        function loadEmbeddedData() {
            reportData = {
  "summary": {
    "total_requests": 2965180,
    "unique_ips": 7058,
    "date_range": {
      "start": "2025-07-16T00:00:19",
      "end": "2025-07-30T14:31:46"
    },
    "avg_requests_per_ip": 420.1161802210258,
    "error_rate": 0.00033724765444256334,
    "peak_hour": [
      "2025-07-22 09:00",
      26959
    ],
    "peak_day": [
      "2025-07-22",
      283926
    ]
  },
  "traffic_patterns": {
    "hourly_pattern": {
      "labels": [
        "00:00",
        "01:00",
        "02:00",
        "03:00",
        "04:00",
        "05:00",
        "06:00",
        "07:00",
        "08:00",
        "09:00",
        "10:00",
        "11:00",
        "12:00",
        "13:00",
        "14:00",
        "15:00",
        "16:00",
        "17:00",
        "18:00",
        "19:00",
        "20:00",
        "21:00",
        "22:00",
        "23:00"
      ],
      "data": [
        46595,
        50849,
        45923,
        43947,
        43690,
        60394,
        62102,
        113824,
        240704,
        269734,
        273668,
        258554,
        211782,
        247878,
        250176,
        227921,
        145192,
        68047,
        56014,
        54938,
        51165,
        48456,
        48549,
        45078
      ]
    },
    "daily_traffic": {
      "labels": [
        "2025-07-16",
        "2025-07-17",
        "2025-07-18",
        "2025-07-19",
        "2025-07-20",
        "2025-07-21",
        "2025-07-22",
        "2025-07-23",
        "2025-07-24",
        "2025-07-25",
        "2025-07-26",
        "2025-07-27",
        "2025-07-28",
        "2025-07-29",
        "2025-07-30"
      ],
      "data": [
        252252,
        236010,
        207717,
        66503,
        63018,
        274473,
        283926,
        251995,
        251083,
        227111,
        88549,
        78097,
        266149,
        247431,
        170866
      ]
    }
  },
  "security_analysis": {
    "error_types": {
      "sql_injection": 19381,
      "path_traversal": 538,
      "xss_attempt": 110
    },
    "failed_auth_ips": {
      "127.0.0.1": 1064,
      "*************": 269,
      "***************": 2,
      "***************": 2,
      "***************": 2,
      "nessus.imtins.com": 2,
      "**************": 2,
      "************": 1,
      "**************": 1,
      "***********": 1
    },
    "large_req_ips": {
      "***************": 108,
      "***************": 84,
      "**************": 84,
      "**************": 82,
      "***************": 80,
      "***************": 74,
      "***************": 73,
      "***************": 72,
      "**************": 66,
      "***************": 62
    },
    "total_security_events": 25805
  },
  "performance_metrics": {
    "response_time": {
      "avg": 1.2345125759650342,
      "median": 0.0,
      "p95": 4,
      "p99": 16
    },
    "response_size": {
      "avg": 20635.26155668907,
      "median": 37.0,
      "total_gb": 51.01402206066996
    }
  },
  "anomalies": [
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 24359,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7606,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21384,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 16260,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************4",
      "requests": 37950,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************1",
      "requests": 6348,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 85686,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************2",
      "requests": 13078,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 12963,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5667,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5200,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 21866,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 20340,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************5",
      "requests": 19353,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************0",
      "requests": 22056,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************1",
      "requests": 13427,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************2",
      "requests": 11292,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 20285,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9954,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************1",
      "requests": 16640,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************3",
      "requests": 17301,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 19415,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************7",
      "requests": 16106,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21440,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 19670,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 19069,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 18585,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 18979,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21323,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21033,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 19924,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21232,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 17819,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21145,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 17557,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "************",
      "requests": 4245,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21586,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 20873,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 20293,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "127.0.0.1",
      "requests": 37694,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5866,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "************",
      "requests": 4431,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 133075,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9025,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 6992,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8032,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7226,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5868,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 21570,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 6222,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5702,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 6162,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 11581,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9786,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9374,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5609,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5622,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 11009,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5083,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 22092,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8518,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 6049,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5705,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8324,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 4429,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************9",
      "requests": 7278,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************6",
      "requests": 11194,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 6443,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 7246,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 7190,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 10998,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************3",
      "requests": 5800,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7184,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8811,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 30135,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8586,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7447,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8667,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************7",
      "requests": 6484,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 15504,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8754,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************9",
      "requests": 8628,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 6595,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 7050,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************0",
      "requests": 14946,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5558,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4600,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5837,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7829,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 7291,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************4",
      "requests": 8239,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7828,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************0",
      "requests": 7168,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 6845,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4537,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 15529,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 4467,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5405,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 19588,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5073,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 14867,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5019,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 6856,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 18996,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9406,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8786,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4985,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 10819,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 17422,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 12647,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************2",
      "requests": 7825,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 4561,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 9583,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4284,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 7766,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************0",
      "requests": 15182,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************9",
      "requests": 10186,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 19653,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 17214,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 18349,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************7",
      "requests": 55430,
      "severity": "high"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 10678,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 11006,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5390,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8776,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5204,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 7112,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8016,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8826,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8433,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4798,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 5354,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************9",
      "requests": 8376,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9582,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 9931,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 14701,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************2",
      "requests": 11197,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************7",
      "requests": 12102,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************8",
      "requests": 4320,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 4818,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************0",
      "requests": 9518,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4253,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 6333,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 4580,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 8337,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************3",
      "requests": 5703,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************7",
      "requests": 8408,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 7067,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 10311,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 8476,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************7",
      "requests": 12387,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************2",
      "requests": 7070,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************4",
      "requests": 9871,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 9067,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 11402,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 7124,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 4278,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "***************",
      "requests": 5440,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************",
      "requests": 13928,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************1",
      "requests": 4817,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "*************",
      "requests": 7493,
      "severity": "medium"
    },
    {
      "type": "high_volume_ip",
      "ip": "**************7",
      "requests": 7531,
      "severity": "medium"
    }
  ],
  "chart_data": {
    "status_codes": {
      "labels": [
        "200",
        "302",
        "400",
        "301",
        "404",
        "500",
        "304",
        "401",
        "201",
        "206",
        "416",
        "504",
        "403",
        "408",
        "405",
        "501"
      ],
      "data": [
        2508012,
        345515,
        21523,
        50085,
        27770,
        925,
        9101,
        1068,
        359,
        6,
        4,
        491,
        305,
        13,
        2,
        1
      ]
    },
    "top_ips": {
      "labels": [
        "*************",
        "***************",
        "***************",
        "**************",
        "**************4",
        "**************1",
        "*************",
        "*************2",
        "***************",
        "***************"
      ],
      "data": [
        24359,
        7606,
        21384,
        16260,
        37950,
        6348,
        85686,
        13078,
        12963,
        5667
      ]
    },
    "top_urls": {
      "labels": [
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...",
        "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl..."
      ],
      "data": [
        1,
        1,
        1,
        1,
        1,
        1,
        114384,
        1,
        1,
        1
      ]
    },
    "geographic": {
      "labels": [
        "internal",
        "cloudflare",
        "external"
      ],
      "data": [
        2638889,
        179214,
        147077
      ]
    }
  },
  "suspicious_ips": [
    {
      "ip": "***************",
      "request_count": 133075,
      "reason": "High request volume"
    },
    {
      "ip": "*************",
      "request_count": 85686,
      "reason": "High request volume"
    },
    {
      "ip": "**************7",
      "request_count": 55430,
      "reason": "High request volume"
    },
    {
      "ip": "**************4",
      "request_count": 37950,
      "reason": "High request volume"
    },
    {
      "ip": "127.0.0.1",
      "request_count": 37694,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 30135,
      "reason": "High request volume"
    },
    {
      "ip": "*************",
      "request_count": 24359,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 22092,
      "reason": "High request volume"
    },
    {
      "ip": "**************0",
      "request_count": 22056,
      "reason": "High request volume"
    },
    {
      "ip": "**************",
      "request_count": 21866,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21586,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21570,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21440,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21384,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21323,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21232,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21145,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 21033,
      "reason": "High request volume"
    },
    {
      "ip": "***************",
      "request_count": 20873,
      "reason": "High request volume"
    },
    {
      "ip": "*************",
      "request_count": 20340,
      "reason": "High request volume"
    }
  ],
  "recent_errors": [
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T19:56:47",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T19:57:51",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T19:59:47",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:01:33",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:04:27",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:06:16",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:10:11",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:10:31",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/WadenaPAS/wa_xrf_vehicleDelete.pl",
      "timestamp": "2025-07-21T20:11:01",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/WadenaPAS/wa_xrf_driverDelete.pl",
      "timestamp": "2025-07-21T20:11:06",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:11:11",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:11:48",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:14:02",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:14:20",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:14:39",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************3",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:21:05",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:24:20",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:24:32",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:24:47",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:38:25",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:39:06",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:39:49",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:39:56",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:42:45",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:42:56",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T20:44:27",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "***********",
      "url": "/imtonline/Claims/Claims_Doc_AWS_Download.pl?key=AWS-bd40de75-fc34-4cfe-a80a-ab5c4e704d35;filename=4.16.25%202024R0156%20Sommer%20transcript%20for%20Penny%20Zacek.msg",
      "timestamp": "2025-07-21T20:57:33",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/css/dropzones.min.css?v=1.5.2",
      "timestamp": "2025-07-21T22:26:35",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/alertifyjs/css/alertify.min.css",
      "timestamp": "2025-07-21T22:26:35",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/alertifyjs/css/themes/bootstrap.min.css",
      "timestamp": "2025-07-21T22:26:35",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/dropzone.min.js?v=1.0.6",
      "timestamp": "2025-07-21T22:26:35",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/alertifyjs/alertify.min.js",
      "timestamp": "2025-07-21T22:26:35",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/ckalert.min.js?v=1.0.1",
      "timestamp": "2025-07-21T22:26:35",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/Claims/claim_scripts_Monetary.js",
      "timestamp": "2025-07-21T22:27:34",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T22:30:56",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T22:32:50",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T22:33:25",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T22:34:16",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T22:46:46",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T22:55:18",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/css/dropzones.min.css?v=1.5.2",
      "timestamp": "2025-07-21T23:00:54",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/alertifyjs/css/alertify.min.css",
      "timestamp": "2025-07-21T23:00:54",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/alertifyjs/css/themes/bootstrap.min.css",
      "timestamp": "2025-07-21T23:00:54",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/dropzone.min.js?v=1.0.6",
      "timestamp": "2025-07-21T23:00:54",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/alertifyjs/alertify.min.js",
      "timestamp": "2025-07-21T23:00:55",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/js/ckalert.min.js?v=1.0.1",
      "timestamp": "2025-07-21T23:00:55",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T23:25:06",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/Claims/claim_scripts_Monetary.js",
      "timestamp": "2025-07-21T23:25:14",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T23:41:24",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    },
    {
      "ip": "**************",
      "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/",
      "timestamp": "2025-07-21T23:55:24",
      "patterns": [
        "sql_injection"
      ],
      "status": "200"
    }
  ],
  "failed_auth_recent": [
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:54",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:55",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:57",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:57",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:58",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:58",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:59",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:59",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:59",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:51:59",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T16:52:12",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T17:15:08",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T17:30:09",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T17:30:09",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T17:35:03",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T17:35:04",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T18:29:46",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T18:29:46",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T18:59:53",
      "status": "401"
    },
    {
      "ip": "127.0.0.1",
      "url": "/api/v1/platform/users/get_auth_token.pl",
      "timestamp": "2025-07-21T18:59:54",
      "status": "401"
    }
  ]
};
            initializeReport();
        }
        
        function initializeReport() {
            updateReportDate();
            populateSummaryStats();
            createCharts();
            populate500ErrorStats();
            populateSecurityAlerts();
            populateDetailedFindings();
        }
        
        function updateReportDate() {
            const now = new Date();
            document.getElementById('report-date').textContent =
                `Generated on ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}`;
        }

        function populateSummaryStats() {
            const summary = reportData.summary;
            const statsContainer = document.getElementById('summary-stats');

            const stats = [
                { title: formatNumber(summary.total_requests), label: 'Total Requests' },
                { title: formatNumber(summary.unique_ips), label: 'Unique IP Addresses' },
                { title: summary.error_rate.toFixed(2) + '%', label: 'Error Rate' },
                { title: Math.round(summary.avg_requests_per_ip), label: 'Avg Requests/IP' }
            ];

            statsContainer.innerHTML = stats.map(stat => `
                <div class="stat-card">
                    <h3>${stat.title}</h3>
                    <p>${stat.label}</p>
                </div>
            `).join('');

            // Add summary text
            const summaryText = document.getElementById('summary-text');
            const startDate = new Date(summary.date_range.start).toLocaleDateString();
            const endDate = new Date(summary.date_range.end).toLocaleDateString();
            const peakHour = summary.peak_hour ? summary.peak_hour[0] : 'N/A';
            const peakRequests = summary.peak_hour ? formatNumber(summary.peak_hour[1]) : 'N/A';

            summaryText.innerHTML = `
                <p><strong>Analysis Period:</strong> ${startDate} to ${endDate}</p>
                <p><strong>Peak Traffic Hour:</strong> ${peakHour} with ${peakRequests} requests</p>
                <p><strong>Security Events Detected:</strong> ${formatNumber(reportData.security_analysis.total_security_events)}</p>
                <p><strong>Anomalies Identified:</strong> ${reportData.anomalies.length}</p>
            `;
        }

        function createCharts() {
            createDailyTrafficChart();
            createHourlyTrafficChart();
            createStatusCodeChart();
            createGeoChart();
            createTopIPsChart();
            createTopURLsChart();
            create500ErrorCharts();
        }

        function createDailyTrafficChart() {
            const ctx = document.getElementById('dailyTrafficChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: reportData.traffic_patterns.daily_traffic.labels,
                    datasets: [{
                        label: 'Daily Requests',
                        data: reportData.traffic_patterns.daily_traffic.data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function createHourlyTrafficChart() {
            const ctx = document.getElementById('hourlyTrafficChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: reportData.traffic_patterns.hourly_pattern.labels,
                    datasets: [{
                        label: 'Requests by Hour',
                        data: reportData.traffic_patterns.hourly_pattern.data,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function createStatusCodeChart() {
            const ctx = document.getElementById('statusCodeChart').getContext('2d');
            const colors = ['#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1', '#fd7e14'];

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: reportData.chart_data.status_codes.labels,
                    datasets: [{
                        data: reportData.chart_data.status_codes.data,
                        backgroundColor: colors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function createGeoChart() {
            const ctx = document.getElementById('geoChart').getContext('2d');

            // Calculate total and percentages
            const total = reportData.chart_data.geographic.data.reduce((sum, value) => sum + value, 0);
            const percentages = reportData.chart_data.geographic.data.map(value =>
                ((value / total) * 100).toFixed(1)
            );

            // Create labels with percentages
            const labelsWithPercentages = reportData.chart_data.geographic.labels.map((label, index) =>
                `${label.charAt(0).toUpperCase() + label.slice(1)} (${percentages[index]}%)`
            );

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labelsWithPercentages,
                    datasets: [{
                        data: reportData.chart_data.geographic.data,
                        backgroundColor: ['#667eea', '#764ba2', '#f093fb'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = reportData.chart_data.geographic.labels[context.dataIndex];
                                    const value = context.parsed;
                                    const percentage = percentages[context.dataIndex];
                                    return `${label.charAt(0).toUpperCase() + label.slice(1)}: ${value.toLocaleString()} requests (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function createTopIPsChart() {
            const ctx = document.getElementById('topIPsChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: reportData.chart_data.top_ips.labels,
                    datasets: [{
                        label: 'Requests',
                        data: reportData.chart_data.top_ips.data,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function createTopURLsChart() {
            const ctx = document.getElementById('topURLsChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: reportData.chart_data.top_urls.labels,
                    datasets: [{
                        label: 'Requests',
                        data: reportData.chart_data.top_urls.data,
                        backgroundColor: 'rgba(118, 75, 162, 0.8)',
                        borderColor: '#764ba2',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function create500ErrorCharts() {
            if (!reportData.error_500_data) return;

            const error500Data = reportData.error_500_data;

            // Top URLs with 500 errors chart
            const urlsCtx = document.getElementById('top500URLsChart').getContext('2d');
            const topUrls = error500Data.top_error_urls.slice(0, 10);

            new Chart(urlsCtx, {
                type: 'bar',
                data: {
                    labels: topUrls.map(item => {
                        const url = item.url;
                        return url.length > 40 ? url.substring(0, 40) + '...' : url;
                    }),
                    datasets: [{
                        label: '500 Errors',
                        data: topUrls.map(item => item.count),
                        backgroundColor: 'rgba(220, 53, 69, 0.8)',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    const index = context[0].dataIndex;
                                    return topUrls[index].url;
                                }
                            }
                        }
                    }
                }
            });

            // Top IPs generating 500 errors chart
            const ipsCtx = document.getElementById('top500IPsChart').getContext('2d');
            const topIPs = error500Data.top_error_ips.slice(0, 10);

            new Chart(ipsCtx, {
                type: 'bar',
                data: {
                    labels: topIPs.map(item => item.ip),
                    datasets: [{
                        label: '500 Errors',
                        data: topIPs.map(item => item.count),
                        backgroundColor: 'rgba(255, 193, 7, 0.8)',
                        borderColor: '#ffc107',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function populate500ErrorStats() {
            if (!reportData.error_500_data) return;

            const error500Data = reportData.error_500_data;
            const statsContainer = document.getElementById('error-500-stats');

            const stats = [
                { title: formatNumber(error500Data.total_500_errors), label: 'Total 500 Errors' },
                { title: formatNumber(Object.keys(error500Data.errors_by_url).length), label: 'Unique URLs' },
                { title: formatNumber(Object.keys(error500Data.errors_by_ip).length), label: 'Unique IPs' },
                { title: error500Data.top_error_urls[0] ? error500Data.top_error_urls[0].count : 0, label: 'Most Errors (Single URL)' }
            ];

            statsContainer.innerHTML = stats.map(stat => `
                <div class="stat-card" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                    <h3>${stat.title}</h3>
                    <p>${stat.label}</p>
                </div>
            `).join('');

            // Add recent 500 errors table
            const recentErrorsContainer = document.getElementById('recent-500-errors');
            if (error500Data.recent_500_errors && error500Data.recent_500_errors.length > 0) {
                let tableHTML = '<h3>🕐 Recent 500 Errors (Click for Details)</h3>';
                tableHTML += '<table class="table"><thead><tr><th>Timestamp</th><th>IP</th><th>Method</th><th>URL</th><th>Response Time</th></tr></thead><tbody>';

                error500Data.recent_500_errors.slice(0, 20).forEach((error, index) => {
                    const timestamp = new Date(error.timestamp).toLocaleString();
                    const urlDisplay = error.url.length > 60 ? error.url.substring(0, 60) + '...' : error.url;
                    tableHTML += `
                        <tr class="clickable-row" onclick="showErrorDetails(${index})" title="Click for detailed information">
                            <td>${timestamp}</td>
                            <td><code>${error.ip}</code></td>
                            <td><span class="badge badge-info">${error.method}</span></td>
                            <td><code>${urlDisplay}</code></td>
                            <td>${error.response_time}ms</td>
                        </tr>
                    `;
                });

                tableHTML += '</tbody></table>';
                tableHTML += '<p style="color: #6c757d; font-style: italic; margin-top: 1rem;">💡 Tip: Click on any row to see detailed error information</p>';
                recentErrorsContainer.innerHTML = tableHTML;
            }
        }

        function populateSecurityAlerts() {
            const alertsContainer = document.getElementById('security-alerts');
            let alertsHTML = '';

            // Add anomalies
            if (reportData.anomalies && reportData.anomalies.length > 0) {
                alertsHTML += '<h3>🚨 Detected Anomalies</h3>';
                reportData.anomalies.forEach(anomaly => {
                    const alertClass = `alert-${anomaly.severity}`;
                    const badgeClass = anomaly.severity === 'high' ? 'badge-danger' :
                                     anomaly.severity === 'medium' ? 'badge-warning' : 'badge-info';

                    alertsHTML += `
                        <div class="alert ${alertClass}">
                            <span class="badge ${badgeClass}">${anomaly.severity.toUpperCase()}</span>
                            <strong>${getAnomalyTitle(anomaly.type)}:</strong> ${getAnomalyDescription(anomaly)}
                        </div>
                    `;
                });
            }

            // Add suspicious IPs
            if (reportData.suspicious_ips && reportData.suspicious_ips.length > 0) {
                alertsHTML += '<h3>🔍 Suspicious IP Addresses</h3>';
                alertsHTML += '<table class="table"><thead><tr><th>IP Address</th><th>Request Count</th><th>Reason</th></tr></thead><tbody>';
                reportData.suspicious_ips.slice(0, 10).forEach(ip => {
                    alertsHTML += `
                        <tr>
                            <td><code>${ip.ip}</code></td>
                            <td>${formatNumber(ip.request_count)}</td>
                            <td>${ip.reason}</td>
                        </tr>
                    `;
                });
                alertsHTML += '</tbody></table>';
            }

            // Add recent security events
            if (reportData.recent_errors && reportData.recent_errors.length > 0) {
                alertsHTML += '<h3>⚠️ Recent Security Events</h3>';
                alertsHTML += '<table class="table"><thead><tr><th>Timestamp</th><th>IP</th><th>Pattern</th><th>URL</th></tr></thead><tbody>';
                reportData.recent_errors.slice(0, 10).forEach(error => {
                    const timestamp = new Date(error.timestamp).toLocaleString();
                    alertsHTML += `
                        <tr>
                            <td>${timestamp}</td>
                            <td><code>${error.ip}</code></td>
                            <td><span class="badge badge-warning">${error.patterns.join(', ')}</span></td>
                            <td><code>${error.url.substring(0, 80)}${error.url.length > 80 ? '...' : ''}</code></td>
                        </tr>
                    `;
                });
                alertsHTML += '</tbody></table>';
            }

            alertsContainer.innerHTML = alertsHTML;
        }

        function populateDetailedFindings() {
            const findingsContainer = document.getElementById('detailed-findings');
            let findingsHTML = '';

            // Performance metrics
            if (reportData.performance_metrics && reportData.performance_metrics.response_time) {
                const perf = reportData.performance_metrics;
                findingsHTML += `
                    <h3>📊 Performance Metrics</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h3>${perf.response_time.avg.toFixed(2)}ms</h3>
                            <p>Average Response Time</p>
                        </div>
                        <div class="stat-card">
                            <h3>${perf.response_time.p95.toFixed(2)}ms</h3>
                            <p>95th Percentile</p>
                        </div>
                        <div class="stat-card">
                            <h3>${formatBytes(perf.response_size.avg)}</h3>
                            <p>Average Response Size</p>
                        </div>
                        <div class="stat-card">
                            <h3>${perf.response_size.total_gb.toFixed(2)} GB</h3>
                            <p>Total Data Transferred</p>
                        </div>
                    </div>
                `;
            }

            // Failed authentication attempts
            if (reportData.failed_auth_recent && reportData.failed_auth_recent.length > 0) {
                findingsHTML += '<h3>🔐 Recent Failed Authentication Attempts</h3>';
                findingsHTML += '<table class="table"><thead><tr><th>Timestamp</th><th>IP Address</th><th>URL</th><th>Status</th></tr></thead><tbody>';
                reportData.failed_auth_recent.forEach(auth => {
                    const timestamp = new Date(auth.timestamp).toLocaleString();
                    findingsHTML += `
                        <tr>
                            <td>${timestamp}</td>
                            <td><code>${auth.ip}</code></td>
                            <td><code>${auth.url.substring(0, 60)}${auth.url.length > 60 ? '...' : ''}</code></td>
                            <td><span class="badge badge-danger">${auth.status}</span></td>
                        </tr>
                    `;
                });
                findingsHTML += '</tbody></table>';
            }

            findingsContainer.innerHTML = findingsHTML;
        }

        // Utility functions
        function formatNumber(num) {
            return new Intl.NumberFormat().format(num);
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getAnomalyTitle(type) {
            const titles = {
                'high_volume_ip': 'High Volume IP Address',
                'high_error_rate': 'High Error Rate',
                'bot_traffic': 'Bot Traffic Detected',
                'suspicious_pattern': 'Suspicious Request Pattern'
            };
            return titles[type] || 'Unknown Anomaly';
        }

        function getAnomalyDescription(anomaly) {
            switch (anomaly.type) {
                case 'high_volume_ip':
                    return `IP ${anomaly.ip} made ${formatNumber(anomaly.requests)} requests`;
                case 'high_error_rate':
                    return `Error rate of ${anomaly.rate.toFixed(2)}% detected`;
                case 'bot_traffic':
                    return `${formatNumber(anomaly.requests)} bot-related requests (${anomaly.percentage.toFixed(2)}% of total)`;
                default:
                    return 'Anomalous behavior detected';
            }
        }

        // Modal functionality for error details
        function showErrorDetails(errorIndex) {
            if (!reportData.error_500_data || !reportData.error_500_data.recent_500_errors) return;

            const error = reportData.error_500_data.recent_500_errors[errorIndex];
            const modal = document.getElementById('errorModal');
            const modalBody = document.getElementById('errorModalBody');

            // Parse URL parameters if present
            let urlParts = error.url.split('?');
            let basePath = urlParts[0];
            let queryParams = '';

            if (urlParts.length > 1) {
                const params = new URLSearchParams(urlParts[1]);
                queryParams = Array.from(params.entries()).map(([key, value]) =>
                    `<div><strong>${key}:</strong> <code>${value}</code></div>`
                ).join('');
            }

            // Get error frequency for this URL
            const urlErrorCount = reportData.error_500_data.errors_by_url[error.url] || 1;
            const ipErrorCount = reportData.error_500_data.errors_by_ip[error.ip] || 1;

            // Determine error severity
            let severity = 'Low';
            let severityColor = '#28a745';
            if (urlErrorCount > 50) {
                severity = 'Critical';
                severityColor = '#dc3545';
            } else if (urlErrorCount > 10) {
                severity = 'High';
                severityColor = '#fd7e14';
            } else if (urlErrorCount > 5) {
                severity = 'Medium';
                severityColor = '#ffc107';
            }

            modalBody.innerHTML = `
                <div class="error-detail-grid">
                    <div class="error-detail-item">
                        <strong>Timestamp</strong>
                        <code>${new Date(error.timestamp).toLocaleString()}</code>
                    </div>
                    <div class="error-detail-item">
                        <strong>IP Address</strong>
                        <code>${error.ip}</code>
                    </div>
                    <div class="error-detail-item">
                        <strong>HTTP Method</strong>
                        <code>${error.method}</code>
                    </div>
                    <div class="error-detail-item">
                        <strong>Response Time</strong>
                        <code>${error.response_time}ms</code>
                    </div>
                    <div class="error-detail-item">
                        <strong>Response Size</strong>
                        <code>${error.size === '-' ? 'No content' : error.size + ' bytes'}</code>
                    </div>
                    <div class="error-detail-item">
                        <strong>Error Severity</strong>
                        <span style="color: ${severityColor}; font-weight: bold;">${severity}</span>
                    </div>
                </div>

                <div class="error-detail-item" style="grid-column: 1 / -1; margin-bottom: 1rem;">
                    <strong>Full URL Path</strong>
                    <code>${basePath}</code>
                </div>

                ${queryParams ? `
                <div class="error-detail-item" style="margin-bottom: 1rem;">
                    <strong>Query Parameters</strong>
                    ${queryParams}
                </div>
                ` : ''}

                <div class="error-detail-grid">
                    <div class="error-detail-item">
                        <strong>Total Errors for this URL</strong>
                        <code>${formatNumber(urlErrorCount)} errors</code>
                        <small style="display: block; margin-top: 0.5rem; color: #6c757d;">
                            ${((urlErrorCount / reportData.error_500_data.total_500_errors) * 100).toFixed(1)}% of all 500 errors
                        </small>
                    </div>
                    <div class="error-detail-item">
                        <strong>Total Errors from this IP</strong>
                        <code>${formatNumber(ipErrorCount)} errors</code>
                        <small style="display: block; margin-top: 0.5rem; color: #6c757d;">
                            ${((ipErrorCount / reportData.error_500_data.total_500_errors) * 100).toFixed(1)}% of all 500 errors
                        </small>
                    </div>
                </div>

                <div class="error-detail-item" style="margin-top: 1rem;">
                    <strong>Troubleshooting Recommendations</strong>
                    <div style="margin-top: 0.5rem;">
                        ${getErrorRecommendations(error, urlErrorCount, ipErrorCount)}
                    </div>
                </div>
            `;

            modal.style.display = 'block';
        }

        function closeErrorModal() {
            document.getElementById('errorModal').style.display = 'none';
        }

        function getErrorRecommendations(error, urlErrorCount, ipErrorCount) {
            let recommendations = [];

            if (urlErrorCount > 50) {
                recommendations.push('🚨 <strong>Critical:</strong> This endpoint is failing frequently. Immediate investigation required.');
            }

            if (error.url.includes('api')) {
                recommendations.push('🔧 Check API endpoint configuration and database connections.');
            }

            if (error.url.includes('billing') || error.url.includes('payment')) {
                recommendations.push('💳 Review payment processing system and third-party integrations.');
            }

            if (error.url.includes('search') || error.url.includes('vin')) {
                recommendations.push('🔍 Check search service availability and database query performance.');
            }

            if (error.ip === '127.0.0.1') {
                recommendations.push('🏠 Internal server error - check application logs and server resources.');
            }

            if (ipErrorCount > 20) {
                recommendations.push('👤 This IP is experiencing many errors - may indicate client-side issues or targeted problems.');
            }

            if (error.response_time > 5000) {
                recommendations.push('⏱️ High response time suggests performance issues or timeouts.');
            }

            if (recommendations.length === 0) {
                recommendations.push('📋 Review server logs for this timestamp and check application error handling.');
            }

            return recommendations.map(rec => `<div style="margin-bottom: 0.5rem;">${rec}</div>`).join('');
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('errorModal');
            if (event.target === modal) {
                closeErrorModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeErrorModal();
            }
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', loadReportData);
    </script>
</body>
</html>
