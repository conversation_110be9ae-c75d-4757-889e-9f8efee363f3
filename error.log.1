PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: /usr/lib/php/20230831/openssl (/usr/lib/php/20230831/openssl: cannot open shared object file: No such file or directory), /usr/lib/php/20230831/openssl.so (/usr/lib/php/20230831/openssl.so: cannot open shared object file: No such file or directory)) in Unknown on line 0
PHP Warning:  PHP Startup: Unable to load dynamic library 'pdo_pgsql' (tried: /usr/lib/php/20230831/pdo_pgsql (/usr/lib/php/20230831/pdo_pgsql: cannot open shared object file: No such file or directory), /usr/lib/php/20230831/pdo_pgsql.so (/usr/lib/php/20230831/pdo_pgsql.so: undefined symbol: pdo_parse_params)) in Unknown on line 0
PHP Warning:  Module "pgsql" is already loaded in Unknown on line 0
[Mon Aug 04 00:00:01.295929 2025] [ssl:warn] [pid 2096] AH01909: amiworryfree.com:80:0 server certificate does NOT include an ID which matches the server name
[Mon Aug 04 00:00:01.296278 2025] [ssl:warn] [pid 2096] AH01909: www.wadenains.com:80:0 server certificate does NOT include an ID which matches the server name
[Mon Aug 04 00:00:01.296811 2025] [mpm_prefork:notice] [pid 2096] AH00163: Apache/2.4.41 (Ubuntu) OpenSSL/1.1.1f mod_perl/2.0.11 Perl/v5.30.0   configured -- resuming normal operations
[Mon Aug 04 00:00:01.296814 2025] [core:notice] [pid 2096] AH00094: Command line: '/usr/sbin/apache2'
[Tue Aug 05 00:00:02.223854 2025] [mpm_prefork:notice] [pid 2096] AH00171: Graceful restart requested, doing restart
[Tue Aug 05 00:00:02.238656 2025] [so:warn] [pid 2096] AH01574: module headers_module is already loaded, skipping
