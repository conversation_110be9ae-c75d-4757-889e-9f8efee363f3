{"summary": {"total_requests": 2965180, "unique_ips": 7058, "date_range": {"start": "2025-07-16T00:00:19", "end": "2025-07-30T14:31:46"}, "avg_requests_per_ip": 420.1161802210258, "error_rate": 0.00033724765444256334, "peak_hour": ["2025-07-22 09:00", 26959], "peak_day": ["2025-07-22", 283926]}, "traffic_patterns": {"hourly_pattern": {"labels": ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"], "data": [46595, 50849, 45923, 43947, 43690, 60394, 62102, 113824, 240704, 269734, 273668, 258554, 211782, 247878, 250176, 227921, 145192, 68047, 56014, 54938, 51165, 48456, 48549, 45078]}, "daily_traffic": {"labels": ["2025-07-16", "2025-07-17", "2025-07-18", "2025-07-19", "2025-07-20", "2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27", "2025-07-28", "2025-07-29", "2025-07-30"], "data": [252252, 236010, 207717, 66503, 63018, 274473, 283926, 251995, 251083, 227111, 88549, 78097, 266149, 247431, 170866]}}, "security_analysis": {"error_types": {"sql_injection": 19381, "path_traversal": 538, "xss_attempt": 110}, "failed_auth_ips": {"127.0.0.1": 1064, "*************": 269, "***************": 2, "***************": 2, "***************": 2, "nessus.imtins.com": 2, "**************": 2, "************": 1, "**************": 1, "***********": 1}, "large_req_ips": {"***************": 108, "***************": 84, "**************": 84, "**************": 82, "**************0": 80, "***************": 74, "**************2": 73, "***************": 72, "**************": 66, "***************": 62}, "total_security_events": 25805}, "performance_metrics": {"response_time": {"avg": 1.2345125759650342, "median": 0.0, "p95": 4, "p99": 16}, "response_size": {"avg": 20635.26155668907, "median": 37.0, "total_gb": 51.01402206066996}}, "anomalies": [{"type": "high_volume_ip", "ip": "*************", "requests": 24359, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7606, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21384, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 16260, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 37950, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************1", "requests": 6348, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 85686, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************2", "requests": 13078, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 12963, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5667, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5200, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 21866, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 20340, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************5", "requests": 19353, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 22056, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 13427, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11292, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 20285, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9954, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 16640, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 17301, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 19415, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 16106, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21440, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 19670, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 19069, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 18585, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 18979, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21323, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21033, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 19924, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21232, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 17819, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21145, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 17557, "severity": "medium"}, {"type": "high_volume_ip", "ip": "************", "requests": 4245, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21586, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 20873, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 20293, "severity": "medium"}, {"type": "high_volume_ip", "ip": "127.0.0.1", "requests": 37694, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5866, "severity": "medium"}, {"type": "high_volume_ip", "ip": "************", "requests": 4431, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 133075, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************2", "requests": 9025, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 6992, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************1", "requests": 8032, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7226, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************08", "requests": 5868, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21570, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 6222, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5702, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6162, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11581, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9786, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9374, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5609, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5622, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************6", "requests": 11009, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5083, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 22092, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8518, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6049, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************0", "requests": 5705, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8324, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4429, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7278, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************6", "requests": 11194, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6443, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7246, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7190, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************2", "requests": 10998, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************3", "requests": 5800, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7184, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8811, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 30135, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8586, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7447, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8667, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 6484, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 15504, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8754, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8628, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.231.142", "requests": 6595, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.80", "requests": 7050, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************0", "requests": 14946, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.231.39", "requests": 5558, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.49", "requests": 4600, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.85", "requests": 5837, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7829, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.22", "requests": 7291, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8239, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7828, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7168, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 6845, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4537, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 15529, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4467, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5405, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 19588, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5073, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 14867, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5019, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6856, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************0", "requests": 18996, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9406, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8786, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4985, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 10819, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 17422, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 12647, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7825, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 4561, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 9583, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4284, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7766, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 15182, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************9", "requests": 10186, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 19653, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************0", "requests": 17214, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 18349, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 55430, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 10678, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11006, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5390, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8776, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5204, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 7112, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8016, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8826, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8433, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4798, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5354, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8376, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9582, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 9931, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 14701, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************2", "requests": 11197, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************7", "requests": 12102, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************8", "requests": 4320, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4818, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************0", "requests": 9518, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4253, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 6333, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4580, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8337, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5703, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************7", "requests": 8408, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7067, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 10311, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8476, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 12387, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7070, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************4", "requests": 9871, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9067, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 11402, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7124, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4278, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5440, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 13928, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************1", "requests": 4817, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 7493, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************7", "requests": 7531, "severity": "medium"}], "chart_data": {"status_codes": {"labels": ["200", "302", "400", "301", "404", "500", "304", "401", "201", "206", "416", "504", "403", "408", "405", "501"], "data": [2508012, 345515, 21523, 50085, 27770, 925, 9101, 1068, 359, 6, 4, 491, 305, 13, 2, 1]}, "top_ips": {"labels": ["*************", "***************", "***************", "**************", "***************", "**************1", "*************", "*************2", "***************", "***************"], "data": [24359, 7606, 21384, 16260, 37950, 6348, 85686, 13078, 12963, 5667]}, "top_urls": {"labels": ["/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl..."], "data": [1, 1, 1, 1, 1, 1, 114384, 1, 1, 1]}, "geographic": {"labels": ["internal", "cloudflare", "external"], "data": [2638889, 179214, 147077]}}, "suspicious_ips": [{"ip": "***************", "request_count": 133075, "reason": "High request volume"}, {"ip": "*************", "request_count": 85686, "reason": "High request volume"}, {"ip": "***************", "request_count": 55430, "reason": "High request volume"}, {"ip": "***************", "request_count": 37950, "reason": "High request volume"}, {"ip": "127.0.0.1", "request_count": 37694, "reason": "High request volume"}, {"ip": "***************", "request_count": 30135, "reason": "High request volume"}, {"ip": "*************", "request_count": 24359, "reason": "High request volume"}, {"ip": "***************", "request_count": 22092, "reason": "High request volume"}, {"ip": "***************", "request_count": 22056, "reason": "High request volume"}, {"ip": "**************", "request_count": 21866, "reason": "High request volume"}, {"ip": "***************", "request_count": 21586, "reason": "High request volume"}, {"ip": "***************", "request_count": 21570, "reason": "High request volume"}, {"ip": "***************", "request_count": 21440, "reason": "High request volume"}, {"ip": "***************", "request_count": 21384, "reason": "High request volume"}, {"ip": "***************", "request_count": 21323, "reason": "High request volume"}, {"ip": "***************", "request_count": 21232, "reason": "High request volume"}, {"ip": "***************", "request_count": 21145, "reason": "High request volume"}, {"ip": "***************", "request_count": 21033, "reason": "High request volume"}, {"ip": "***************", "request_count": 20873, "reason": "High request volume"}, {"ip": "*************", "request_count": 20340, "reason": "High request volume"}], "recent_errors": [{"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T19:56:47", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T19:57:51", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T19:59:47", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:01:33", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:04:27", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:06:16", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:10:11", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:10:31", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/WadenaPAS/wa_xrf_vehicleDelete.pl", "timestamp": "2025-07-21T20:11:01", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/WadenaPAS/wa_xrf_driverDelete.pl", "timestamp": "2025-07-21T20:11:06", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:11:11", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:11:48", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:14:02", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:14:20", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:14:39", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:21:05", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:24:20", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:24:32", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:24:47", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:38:25", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:39:06", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:39:49", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:39:56", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:42:45", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:42:56", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:44:27", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***********", "url": "/imtonline/Claims/Claims_Doc_AWS_Download.pl?key=AWS-bd40de75-fc34-4cfe-a80a-ab5c4e704d35;filename=4.16.25%202024R0156%20Sommer%20transcript%20for%20Penny%20Zacek.msg", "timestamp": "2025-07-21T20:57:33", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/css/dropzones.min.css?v=1.5.2", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/alertify.min.css", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/themes/bootstrap.min.css", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/dropzone.min.js?v=1.0.6", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/alertify.min.js", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/ckalert.min.js?v=1.0.1", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/Claims/claim_scripts_Monetary.js", "timestamp": "2025-07-21T22:27:34", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:30:56", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:32:50", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:33:25", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:34:16", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:46:46", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:55:18", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/css/dropzones.min.css?v=1.5.2", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/alertify.min.css", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/themes/bootstrap.min.css", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/dropzone.min.js?v=1.0.6", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/alertify.min.js", "timestamp": "2025-07-21T23:00:55", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/ckalert.min.js?v=1.0.1", "timestamp": "2025-07-21T23:00:55", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T23:25:06", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/Claims/claim_scripts_Monetary.js", "timestamp": "2025-07-21T23:25:14", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T23:41:24", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T23:55:24", "patterns": ["sql_injection"], "status": "200"}], "failed_auth_recent": [{"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:54", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:55", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:57", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:57", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:58", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:58", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:52:12", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:15:08", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:30:09", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:30:09", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:35:03", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:35:04", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:29:46", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:29:46", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:59:53", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:59:54", "status": "401"}], "error_500_data": {"total_500_errors": 925, "errors_by_url": {"/imtonline/Claims/Claims_Tracker.pl": 10, "/imtonline/WadenaPAS/wa_xrf_Roadside.pl?vehNum=all&fromclient=1": 2, "/imtonline/uassist/UAssistEngine.pl": 4, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php": 369, "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary": 34, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC2020PN576406&action=1": 1, "/imtonline/WCom/WBOP.pl": 11, "/imtonline/api/v1/document-management/upload/": 6, "/imtonline/api/v1/document-management/upload/process/": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2": 8, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2M2N179Y1EC088864&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2": 4, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X02J884839&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWD49XXPR256419&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X71J882746&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWD49X7GR103903&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X14R061647&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1e9bf3220es230494&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2014&action=2&class=76": 1, "/imtonline/WCom/CV_Engine.pl": 11, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VMBED29SM681045&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4ZECH1623R1325815&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4PGBJ18196L027209&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=430FD1622PM075279&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2": 3, "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=": 43, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=50XBE2420SA049619&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKBD59XXLJ551128&action=1": 1, "/imtonline/insuredPortal/insuredPortal.pl": 13, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WMBE182XR1008816&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUAMH30EY020064&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2": 7, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2": 4, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2": 7, "/imtonline/WCom/GL_Engine.pl": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LWB2824XP053169&action=1": 1, "/imtonline/WC/WCEngine.pl?whereAreYouGoing=WC_print_main;func=WC_check_for_stored_pdf;sessionID=1952822.141266": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2": 5, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2023&action=2&class=2": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2": 7, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5nhuamh30ey020064&action=1": 1, "/imtonline/HO_HO0546.pl": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSCESBR67C504438&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1HSDJAPR7FH743085&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LWB262X8P147982&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1HSHBAAN3SH692456&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=76": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3295790&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-02-17&DEC_TYPE=3&origin=ios": 1, "/imtonline/soap_service.pl": 3, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3FDPF75462MA10044&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5FTLE162031019116&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC2229NN574759&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=2": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=76": 3, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4L5SC1926TF077008&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2026&action=2&class=76": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1WAMAF1W46A243758&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2": 5, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2017&action=2&class=2": 5, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2023&action=2&class=76": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4J6UF1828YB001358&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1N9LB3929TA128040&action=1": 1, "/imtonline/insuredPortal/mobileAppLogIn.pl": 15, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X63J391698&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1994&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2": 5, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2006&action=2&class=2": 2, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2002&action=2&class=2": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2": 6, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4X4TSE01931015621&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTU222481014527&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2000&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=76": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5H21412K22W002426&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1W9BP1825JH160252&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1218KM650533&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1624LM658150&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1W9BP1820LH160131&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7K61E1621MH001297&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1W9AP2522MH160235&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7K61E1623MH001298&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1W9AP2528NH160127&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUJA6CK2ADAM7599&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUYDMDB8XPA11982&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUP2LYB6KP343673&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4J6US1214XB901412&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4J6US1018XB903201&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533DB1627HC261109&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWUT1623KN516084&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4J6MX20255B066100&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52WBU1824GR012325&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2": 5, "/imtonline/Common/GAS/getAccountInfo.pl": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7UZFD2222RB000070&action=1": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3444978&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2024-09-19&DEC_TYPE=3&origin=andapp": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5RVBB1624SP143342&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2sFFL3367M1052050&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2sffl3367m1052050&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=76": 3, "/imtonline/insuredPortal/claim.pl": 5, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EB26P1010111&action=1": 1, "/imtonline/Common/GAS/accountExists.pl": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2011&action=2&class=2": 4, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53BCTEA29HA029604&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JMUS172751019924&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=76": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5M3BE1428H1073313&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2": 5, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4L5SC2527TF073304&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWHP3020RN591927&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=542BE323XCB001897&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWDD1620PN589269&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5R8GN32278M010084&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EB21T1013752&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7lzbe2420sw120583&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FDPF7DE2PDF09193&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3FRXF75T07V544169&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52LBE1214FE038715&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1211RM676272&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52LBE1216JE069103&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52LBE1218JE066512&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LUB16221P070547&action=1": 1, "/imtonline/Common/Platform/cicsAgentMasterAPI.pl": 2, "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10437133&date_processed=2025-07-25&dec_type=B&doc_group=inv": 1, "/one_time_payment.pl": 43, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JHBT23295D000224&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1": 4, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUYDSEB9VP823563&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1": 4, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=WHM2L5V&ACTG1=1&LINE_OF_BUSINESS1=HO&POLICY_EFF_DATE1=2025-07-07&DEC_TYPE=3&origin=andapp": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4VRHD2028XM006523&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XPHDU9X97N683830&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5VGFR3625FL004418&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSFHDPR6PC062494&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSFHAER1XC024057&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XPWD49X9DD196037&action=1": 1, "/imtonline/PERS/HM/HM_Engine.pl": 3, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EB26K1003250&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533SC1219FC249509&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=76": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53MDX1423DB002729&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE2920DM601463&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1G9CA453XPS139027&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533SC1215EC232172&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5ERBU0816EM075854&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5KGBBTF29E1012297&action=1": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3246326&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2004&action=2&class=2": 1, "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10246828&date_processed=2025-07-25&dec_type=B&doc_group=inv": 1, "/imtonline/smartreport/SREngine.pl": 2, "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing": 6, "/imtonline/insuredPortal/insuredPortal.pl?load=IP_MyDocuments&origin=mpp": 1, "/login/reset.pl?forgotpass=1&str=JNPpigAKZk1ukTJWKiRA4Nb_6OTm9tvM2wZKoreK&email=<EMAIL>&username=ACBRUNO&origin=mppweb": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2565226": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2325088": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2175145": 3, "/imtonline/PERS/HM/HM_Engine.pl?gid=2615678": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2625766": 2, "/imtonline/PERS/HM/HM_Engine.pl?gid=2259697": 2, "/imtonline/PERS/HM/HM_Engine.pl?gid=2625772": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2625775": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2625778": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2608385": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2313300": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2456960": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2541960": 1, "/imtonline/PERS/HM/HM_Engine.pl?gid=2625415": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52WBU1215BR002332&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5L3CX2024JL000053&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5FTFA3730G1000597&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1A9L64121EA245284&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1K9GJ24443H048313&action=1": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3479798&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-10&DEC_TYPE=3&origin=andapp": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUUS423GW060002&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1K9GJ36691H048079&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4YMCL12159M003558&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1k9gj36691h048079&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u01s14215a023100&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EC25N1008393&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2": 3, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2013&action=2&class=2": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4X4TSEV21YN024704&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JMUS1627NS010975&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FVACXBSXDDFB9917&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7LZBE1621NW109801&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u5cc1822ve00336a&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4U5CC1822VE00336A&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=542BC1626FB011911&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=58e1w1422s2019826&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2014&action=2&class=2": 1, "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10491423&date_processed=2025-07-23&dec_type=B&doc_group=inv": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=13SCH2422G1CA1228&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3AKJGEDV1HSHZ5928&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3AKJGEDV5HSHZ5916&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56vbe1620gm628794&action=1": 1, "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv": 3, "/imtonline/uassist/UAssistEngine.pl?trans_id=2298443&load=UATransView&oldUnd=36&post=1&setNewUnd=1&newUnd=66": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4YDT33728LH939056&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE162XFM620068&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7G1BE2025LE006865&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4p5de222041060220&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=76": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56ZL1UG26JP000095&action=1": 1, "/imtonline/WCom/CP_Engine.pl": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1975&action=2&class=2": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1dgrs16237m074781&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1421SM677336&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de27n1211340&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53bltea25ga021594&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de24j1179182&action=1": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=ios": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7UZFD2228S1007018&action=1": 1, "/agent_dashboard/policy-center/pd_mobiuspdf.pl": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WFBE1623RS028815&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUFA427SN073192&action=1": 2, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1NPXGGGG70D605950&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1ygus1414rb268377&action=1": 1, "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=58E1W1829S1017118&action=1": 1, "/imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R": 4, "/imtonline/Common/InvoiceCloud/get_ic_pay_url.pl": 1, "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10324832&date_processed=2024-09-03&dec_type=B&doc_group=inv": 1, "/api/v1/platform/users/get_user_info.pl?rq_dept_code=CLH": 1, "/login/xhr_logindecide.pl": 1}, "errors_by_ip": {"***************": 16, "***************": 6, "**************": 6, "***********": 1, "**************": 7, "**************": 4, "127.0.0.1": 292, "**************": 2, "************": 1, "***************": 6, "**************": 2, "**************": 1, "**************": 1, "**************": 1, "***********": 1, "************": 1, "***************": 13, "*************": 2, "**************": 2, "**************": 15, "************": 1, "*************": 1, "**************": 32, "************": 1, "*************": 1, "***********4": 2, "*************": 1, "**************": 3, "************": 1, "*************": 7, "*************": 5, "***************": 14, "***************": 13, "***************": 13, "**************": 4, "***************": 11, "**************": 4, "***********": 1, "*************": 6, "************": 1, "************": 1, "**************": 1, "**************": 1, "***************": 17, "*************": 7, "**************": 20, "***************": 2, "**************": 1, "172.70.130.167": 1, "*************": 8, "172.69.6.76": 1, "172.70.126.212": 1, "172.70.130.178": 1, "104.23.187.243": 2, "**************": 19, "***************": 3, "172.70.126.41": 1, "162.158.137.153": 1, "162.158.137.58": 1, "162.158.137.207": 2, "172.69.58.165": 1, "**************": 16, "***************": 8, "************": 2, "141.101.109.205": 1, "**************": 5, "192.168.153.113": 2, "162.158.212.156": 1, "172.71.254.250": 3, "172.71.254.251": 3, "**************": 3, "***************": 14, "192.168.152.86": 4, "172.71.1.135": 1, "162.158.212.153": 1, "*************": 5, "172.69.58.199": 1, "172.69.7.39": 1, "172.69.58.93": 1, "192.168.153.4": 2, "*************": 5, "172.71.255.136": 3, "***************": 16, "************": 1, "172.69.58.81": 1, "172.69.59.4": 1, "172.71.1.130": 1, "172.71.1.147": 1, "162.158.137.46": 1, "172.69.58.124": 1, "162.158.212.162": 1, "141.101.109.176": 1, "162.158.137.224": 1, "162.158.212.148": 1, "162.158.212.149": 1, "172.69.58.235": 1, "172.69.58.234": 1, "172.71.1.191": 1, "172.69.58.187": 1, "************7": 1, "172.69.59.79": 1, "172.69.6.161": 1, "172.70.127.225": 1, "172.71.255.7": 1, "172.69.17.85": 2, "172.70.176.111": 1, "172.71.1.163": 1, "192.168.231.38": 5, "172.71.254.114": 2, "**************": 2, "172.69.17.41": 1, "172.69.17.40": 1, "*************": 5, "**************": 2, "**************": 3, "162.158.159.211": 1, "***************": 3, "172.69.59.94": 1, "172.69.58.97": 1, "108.162.216.242": 1, "172.69.59.43": 1, "***************": 6, "172.71.254.243": 1, "***************": 4, "172.70.126.170": 1, "172.70.131.207": 1, "172.69.59.206": 1, "*************3": 1, "172.70.127.43": 1, "172.69.58.89": 1, "172.70.127.107": 1, "172.70.100.129": 1, "172.71.254.241": 1, "172.69.17.34": 1, "172.69.59.226": 1, "172.69.58.84": 1, "172.70.179.182": 1, "**************": 5, "172.70.126.217": 1, "172.70.131.200": 2, "172.70.131.199": 1, "162.158.158.154": 1, "172.69.58.28": 1, "192.168.153.49": 2, "**************": 1, "108.162.216.141": 1, "***********3": 2, "***********2": 1, "172.69.58.154": 1, "172.69.58.232": 2, "172.69.59.224": 1, "172.69.17.45": 1, "*************": 2, "162.158.158.121": 1, "172.69.59.128": 2, "172.69.59.127": 1, "***************": 2, "***************": 2, "192.168.153.80": 2, "172.69.59.139": 2, "172.71.1.148": 1, "172.69.58.185": 1, "172.70.126.56": 1, "172.70.126.107": 1, "162.158.158.152": 1, "108.162.216.217": 1, "108.162.216.216": 1, "162.158.159.166": 1, "172.70.178.28": 3, "172.70.130.173": 1, "172.69.59.125": 1, "162.158.158.6": 1, "172.71.1.165": 1, "172.70.127.141": 2, "172.68.150.30": 1, "192.168.152.93": 1, "172.69.7.19": 1, "172.70.176.148": 1, "172.69.59.35": 1, "172.68.34.34": 1, "172.70.100.207": 1, "***************": 5, "172.69.6.253": 1, "172.69.58.172": 1, "192.168.153.85": 1, "192.168.152.27": 7, "172.70.130.176": 1, "172.71.1.152": 1, "172.70.126.106": 2, "**************": 4, "141.101.109.243": 1, "172.71.255.14": 1, "162.158.137.38": 1, "162.158.212.243": 1, "162.158.137.36": 1, "162.158.137.170": 1, "141.101.109.223": 1, "162.158.137.212": 1, "162.158.212.250": 1, "162.158.137.185": 1, "162.158.212.234": 2, "**************": 1, "141.101.109.130": 1, "172.71.254.70": 1, "172.71.254.71": 1, "162.158.62.68": 1, "141.101.109.150": 1, "*************5": 2, "172.70.176.21": 3, "172.70.176.20": 1, "172.69.58.116": 1, "172.71.1.156": 1, "172.69.58.153": 1, "172.69.58.72": 1, "172.71.254.58": 1, "192.168.224.140": 1, "192.168.231.148": 1, "172.69.17.108": 1, "172.69.17.32": 1, "172.69.6.113": 1, "172.69.6.112": 1, "141.101.109.244": 1, "172.70.179.193": 1, "192.168.153.7": 2, "************": 2, "************": 1, "**************": 1, "*************": 1, "**************": 2, "************": 1, "**************": 1, "**************": 2, "*************": 1, "***************": 1, "***************": 1, "**************": 1, "*************": 1, "**************": 1, "***********": 1, "***************": 1, "***************": 1, "***************": 1, "***************": 1, "***************": 1, "************": 1, "***************": 1, "**************": 4, "**************": 1, "*************": 1, "**************": 1}, "errors_by_hour": {"2025-07-30 00:00": 10, "2025-07-30 02:00": 1, "2025-07-30 08:00": 8, "2025-07-30 09:00": 12, "2025-07-30 10:00": 2, "2025-07-30 11:00": 15, "2025-07-30 12:00": 1, "2025-07-30 13:00": 4, "2025-07-30 14:00": 2, "2025-07-29 07:00": 1, "2025-07-29 08:00": 8, "2025-07-29 09:00": 12, "2025-07-29 10:00": 6, "2025-07-29 11:00": 4, "2025-07-29 12:00": 6, "2025-07-29 13:00": 12, "2025-07-29 14:00": 41, "2025-07-29 15:00": 12, "2025-07-29 16:00": 3, "2025-07-29 18:00": 6, "2025-07-29 19:00": 1, "2025-07-20 21:00": 1, "2025-07-19 09:00": 2, "2025-07-19 11:00": 1, "2025-07-19 22:00": 13, "2025-07-19 23:00": 3, "2025-07-18 07:00": 3, "2025-07-18 08:00": 2, "2025-07-18 09:00": 14, "2025-07-18 10:00": 12, "2025-07-18 11:00": 20, "2025-07-18 12:00": 4, "2025-07-18 13:00": 2, "2025-07-18 14:00": 3, "2025-07-18 15:00": 3, "2025-07-18 16:00": 1, "2025-07-18 18:00": 1, "2025-07-18 22:00": 2, "2025-07-17 09:00": 11, "2025-07-17 08:00": 2, "2025-07-17 10:00": 17, "2025-07-17 11:00": 17, "2025-07-17 12:00": 27, "2025-07-17 13:00": 30, "2025-07-17 14:00": 2, "2025-07-17 15:00": 9, "2025-07-17 16:00": 8, "2025-07-17 17:00": 6, "2025-07-16 08:00": 3, "2025-07-16 09:00": 1, "2025-07-16 10:00": 3, "2025-07-16 11:00": 14, "2025-07-16 12:00": 8, "2025-07-16 13:00": 4, "2025-07-16 14:00": 19, "2025-07-16 15:00": 15, "2025-07-16 16:00": 10, "2025-07-16 19:00": 2, "2025-07-28 07:00": 1, "2025-07-28 08:00": 3, "2025-07-28 09:00": 2, "2025-07-28 10:00": 8, "2025-07-28 11:00": 7, "2025-07-28 12:00": 8, "2025-07-28 13:00": 1, "2025-07-28 14:00": 10, "2025-07-28 15:00": 11, "2025-07-28 16:00": 4, "2025-07-28 17:00": 1, "2025-07-28 21:00": 2, "2025-07-27 14:00": 1, "2025-07-27 23:00": 1, "2025-07-26 00:00": 4, "2025-07-26 01:00": 5, "2025-07-25 06:00": 1, "2025-07-25 07:00": 5, "2025-07-25 08:00": 27, "2025-07-25 09:00": 44, "2025-07-25 10:00": 26, "2025-07-25 11:00": 9, "2025-07-25 12:00": 3, "2025-07-25 13:00": 3, "2025-07-25 14:00": 3, "2025-07-25 15:00": 3, "2025-07-25 16:00": 5, "2025-07-24 07:00": 1, "2025-07-24 08:00": 9, "2025-07-24 09:00": 8, "2025-07-24 10:00": 10, "2025-07-24 11:00": 6, "2025-07-24 12:00": 4, "2025-07-24 13:00": 21, "2025-07-24 14:00": 1, "2025-07-24 15:00": 3, "2025-07-24 16:00": 2, "2025-07-24 18:00": 1, "2025-07-23 08:00": 4, "2025-07-23 09:00": 6, "2025-07-23 10:00": 17, "2025-07-23 11:00": 5, "2025-07-23 12:00": 4, "2025-07-23 13:00": 5, "2025-07-23 14:00": 12, "2025-07-23 15:00": 4, "2025-07-23 16:00": 8, "2025-07-22 06:00": 6, "2025-07-22 07:00": 1, "2025-07-22 08:00": 3, "2025-07-22 09:00": 5, "2025-07-22 10:00": 14, "2025-07-22 11:00": 7, "2025-07-22 12:00": 4, "2025-07-22 13:00": 2, "2025-07-22 14:00": 4, "2025-07-22 15:00": 16, "2025-07-22 16:00": 10, "2025-07-22 17:00": 1, "2025-07-22 19:00": 1, "2025-07-22 20:00": 1, "2025-07-21 08:00": 4, "2025-07-21 09:00": 9, "2025-07-21 10:00": 5, "2025-07-21 11:00": 8, "2025-07-21 12:00": 8, "2025-07-21 13:00": 7, "2025-07-21 14:00": 9, "2025-07-21 15:00": 5, "2025-07-21 16:00": 2, "2025-07-21 18:00": 1, "2025-07-21 23:00": 1}, "errors_by_day": {"2025-07-30": 55, "2025-07-29": 112, "2025-07-20": 1, "2025-07-19": 19, "2025-07-18": 67, "2025-07-17": 129, "2025-07-16": 79, "2025-07-28": 58, "2025-07-27": 2, "2025-07-26": 9, "2025-07-25": 129, "2025-07-24": 66, "2025-07-23": 65, "2025-07-22": 75, "2025-07-21": 59}, "recent_500_errors": [{"timestamp": "2025-07-22T20:01:25", "ip": "***************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "*************** - - [22/Jul/2025:20:01:25 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T19:49:50", "ip": "***************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=ios", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "*************** - - [22/Jul/2025:19:49:50 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=ios HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T17:11:28", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************* - - [22/Jul/2025:17:11:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:50:38", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "protocol": "HTTP/1.1", "response_time": 604, "size": "532", "raw_log_line": "************* - - [22/Jul/2025:16:50:38 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-22T16:48:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de24j1179182&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:48:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de24j1179182&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:44:51", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:44:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:44:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53bltea25ga021594&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:44:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53bltea25ga021594&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:35:08", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=76", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:35:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:34:58", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de27n1211340&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:34:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de27n1211340&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:30:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:30:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:28:18", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:28:18 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:01:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1421SM677336&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:01:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1421SM677336&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:01:06", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1dgrs16237m074781&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:16:01:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1dgrs16237m074781&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:56:25", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:56:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:55:05", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:55:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:53:54", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:53:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:52:30", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:52:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:52:28", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************* - - [22/Jul/2025:15:52:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:51:28", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************ - - [22/Jul/2025:15:51:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:51:04", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:15:51:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:50:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:50:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:48:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1975&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:15:48:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1975&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:46:05", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************ - - [22/Jul/2025:15:46:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:22:57", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/CP_Engine.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "685", "raw_log_line": "************* - - [22/Jul/2025:15:22:57 -0500] \"POST /imtonline/WCom/CP_Engine.pl HTTP/1.1\" 500 685 0"}, {"timestamp": "2025-07-22T15:21:45", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:21:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:20:01", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [22/Jul/2025:15:20:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:02:49", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:15:02:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:02:33", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:15:02:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:02:27", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************* - - [22/Jul/2025:15:02:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T14:51:56", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [22/Jul/2025:14:51:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T14:33:23", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "************ - - [22/Jul/2025:14:33:23 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T14:33:18", "ip": "**************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "************** - - [22/Jul/2025:14:33:18 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T14:16:40", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************ - - [22/Jul/2025:14:16:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T13:23:36", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [22/Jul/2025:13:23:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T13:20:49", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [22/Jul/2025:13:20:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T12:52:27", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "protocol": "HTTP/1.1", "response_time": 603, "size": "532", "raw_log_line": "************* - - [22/Jul/2025:12:52:27 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-22T12:50:06", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [22/Jul/2025:12:50:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T12:16:10", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 603, "size": "532", "raw_log_line": "*************** - - [22/Jul/2025:12:16:10 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-22T12:14:04", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 607, "size": "532", "raw_log_line": "*************** - - [22/Jul/2025:12:14:04 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-07-22T11:28:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56ZL1UG26JP000095&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [22/Jul/2025:11:28:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56ZL1UG26JP000095&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:21:33", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [22/Jul/2025:11:21:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T23:20:17", "ip": "**************", "method": "POST", "url": "/login/xhr_logindecide.pl", "protocol": "HTTP/1.1", "response_time": 1, "size": "532", "raw_log_line": "************** - - [21/Jul/2025:23:20:17 -0500] \"POST /login/xhr_logindecide.pl HTTP/1.1\" 500 532 1"}, {"timestamp": "2025-07-21T18:41:32", "ip": "*************", "method": "POST", "url": "/imtonline/soap_service.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "610", "raw_log_line": "************* - - [21/Jul/2025:18:41:32 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-07-21T16:59:56", "ip": "127.0.0.1", "method": "GET", "url": "/api/v1/platform/users/get_user_info.pl?rq_dept_code=CLH", "protocol": "HTTP/1.1", "response_time": 44, "size": "270", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:16:59:56 -0500] \"GET /api/v1/platform/users/get_user_info.pl?rq_dept_code=CLH HTTP/1.1\" 500 270 44"}, {"timestamp": "2025-07-21T16:08:07", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:16:08:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T15:28:42", "ip": "**************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10324832&date_processed=2024-09-03&dec_type=B&doc_group=inv", "protocol": "HTTP/1.1", "response_time": 0, "size": "85", "raw_log_line": "************** - - [21/Jul/2025:15:28:42 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10324832&date_processed=2024-09-03&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-21T15:22:40", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [21/Jul/2025:15:22:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T15:19:05", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "protocol": "HTTP/1.1", "response_time": 606, "size": "532", "raw_log_line": "*************** - - [21/Jul/2025:15:19:05 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-21T15:13:50", "ip": "***************", "method": "POST", "url": "/imtonline/Common/InvoiceCloud/get_ic_pay_url.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "*************** - - [21/Jul/2025:15:13:50 -0500] \"POST /imtonline/Common/InvoiceCloud/get_ic_pay_url.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-21T15:03:04", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [21/Jul/2025:15:03:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T14:44:33", "ip": "**************", "method": "GET", "url": "/imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R", "protocol": "HTTP/1.1", "response_time": 0, "size": "1279", "raw_log_line": "************** - - [21/Jul/2025:14:44:33 -0500] \"GET /imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R HTTP/1.1\" 500 1279 0"}, {"timestamp": "2025-07-21T14:44:21", "ip": "**************", "method": "GET", "url": "/imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R", "protocol": "HTTP/1.1", "response_time": 0, "size": "1279", "raw_log_line": "************** - - [21/Jul/2025:14:44:21 -0500] \"GET /imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R HTTP/1.1\" 500 1279 0"}, {"timestamp": "2025-07-21T14:44:03", "ip": "**************", "method": "GET", "url": "/imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R", "protocol": "HTTP/1.1", "response_time": 0, "size": "1279", "raw_log_line": "************** - - [21/Jul/2025:14:44:03 -0500] \"GET /imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R HTTP/1.1\" 500 1279 0"}, {"timestamp": "2025-07-21T14:43:58", "ip": "**************", "method": "GET", "url": "/imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R", "protocol": "HTTP/1.1", "response_time": 0, "size": "1279", "raw_log_line": "************** - - [21/Jul/2025:14:43:58 -0500] \"GET /imtonline/uassist/UAssistEngine.pl?trans_id=2285644&load=UATransView&policy_number=CVU8973&eff_date=2024-08-04&type=A&trans_date=2025-06-17&oldPolicyStatus=H&post=1&setPolicyStatus=1&platform_version_uuid=&newPolicyStatus=R HTTP/1.1\" 500 1279 0"}, {"timestamp": "2025-07-21T14:40:07", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:14:40:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T14:39:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2011&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:14:39:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2011&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T14:11:19", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:14:11:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T14:11:08", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [21/Jul/2025:14:11:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T14:07:38", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:14:07:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:57:17", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:13:57:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:53:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=58E1W1829S1017118&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:13:53:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=58E1W1829S1017118&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:45:42", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:13:45:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:22:56", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [21/Jul/2025:13:22:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:10:32", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:13:10:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:08:25", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:13:08:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T13:07:25", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1ygus1414rb268377&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:13:07:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1ygus1414rb268377&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:55:27", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "************** - - [21/Jul/2025:12:55:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:26:28", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:26:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:25:53", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:25:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:08:50", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:08:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:08:46", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:08:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:07:50", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:07:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:07:05", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:07:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T12:01:26", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:12:01:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T11:48:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:11:48:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T11:47:36", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:11:47:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T11:45:00", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1NPXGGGG70D605950&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:11:45:00 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1NPXGGGG70D605950&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T11:31:51", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUFA427SN073192&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:11:31:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUFA427SN073192&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T11:24:14", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 604, "size": "532", "raw_log_line": "************ - - [21/Jul/2025:11:24:14 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-21T11:23:48", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 605, "size": "532", "raw_log_line": "*************** - - [21/Jul/2025:11:23:48 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-21T11:21:52", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 604, "size": "532", "raw_log_line": "*************** - - [21/Jul/2025:11:21:52 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-21T11:00:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUFA427SN073192&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:11:00:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUFA427SN073192&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T10:39:05", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WFBE1623RS028815&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:10:39:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WFBE1623RS028815&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T10:29:45", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:10:29:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T10:29:26", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:10:29:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T10:02:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:10:02:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T10:02:17", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:10:02:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T09:57:19", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:09:57:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T09:48:55", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:09:48:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T09:43:58", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2006&action=2&class=2", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:09:43:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2006&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T09:43:31", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:09:43:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T09:41:28", "ip": "***************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "*************** - - [21/Jul/2025:09:41:28 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-21T09:24:14", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "*************** - - [21/Jul/2025:09:24:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T09:14:31", "ip": "***********", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "*********** - - [21/Jul/2025:09:14:31 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-21T09:14:25", "ip": "**************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl", "protocol": "HTTP/1.1", "response_time": 0, "size": "532", "raw_log_line": "************** - - [21/Jul/2025:09:14:25 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-21T09:07:44", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7UZFD2228S1007018&action=1", "protocol": "HTTP/1.1", "response_time": 0, "size": "-", "raw_log_line": "127.0.0.1 - - [21/Jul/2025:09:07:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7UZFD2228S1007018&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-21T08:58:20", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "protocol": "HTTP/1.1", "response_time": 605, "size": "532", "raw_log_line": "************* - - [21/Jul/2025:08:58:20 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-21T08:26:09", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 604, "size": "532", "raw_log_line": "************** - - [21/Jul/2025:08:26:09 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-21T08:21:06", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "protocol": "HTTP/1.1", "response_time": 603, "size": "532", "raw_log_line": "************ - - [21/Jul/2025:08:21:06 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-21T08:19:35", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "protocol": "HTTP/1.1", "response_time": 605, "size": "532", "raw_log_line": "*************** - - [21/Jul/2025:08:19:35 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 605"}], "top_error_urls": [{"url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "count": 369}, {"url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "count": 43}, {"url": "/one_time_payment.pl", "count": 43}, {"url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "count": 34}, {"url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "count": 15}, {"url": "/imtonline/insuredPortal/insuredPortal.pl", "count": 13}, {"url": "/imtonline/WCom/WBOP.pl", "count": 11}, {"url": "/imtonline/WCom/CV_Engine.pl", "count": 11}, {"url": "/imtonline/Claims/Claims_Tracker.pl", "count": 10}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "count": 8}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "count": 7}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "count": 7}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "count": 7}, {"url": "/imtonline/api/v1/document-management/upload/", "count": 6}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "count": 6}, {"url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "count": 6}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "count": 5}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2", "count": 5}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2017&action=2&class=2", "count": 5}, {"url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2", "count": 5}], "top_error_ips": [{"ip": "127.0.0.1", "count": 292}, {"ip": "**************", "count": 32}, {"ip": "**************", "count": 20}, {"ip": "**************", "count": 19}, {"ip": "***************", "count": 17}, {"ip": "***************", "count": 16}, {"ip": "**************", "count": 16}, {"ip": "***************", "count": 16}, {"ip": "**************", "count": 15}, {"ip": "***************", "count": 14}, {"ip": "***************", "count": 14}, {"ip": "***************", "count": 13}, {"ip": "***************", "count": 13}, {"ip": "***************", "count": 13}, {"ip": "***************", "count": 11}, {"ip": "*************", "count": 8}, {"ip": "***************", "count": 8}, {"ip": "**************", "count": 7}, {"ip": "*************", "count": 7}, {"ip": "*************", "count": 7}]}}