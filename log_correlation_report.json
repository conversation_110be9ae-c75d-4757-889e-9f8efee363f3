{"summary": {"total_500_errors": 809, "total_error_log_entries": 6, "successful_correlations": 0, "correlation_rate": 0.0, "avg_correlation_score": 0.0, "correlation_methods": {}}, "correlations": [], "uncorrelated_500_errors": [{"timestamp": "2025-08-05T00:38:41", "ip": "*************", "method": "GET", "url": "/imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926", "response_time": 63, "raw_line": "************* - - [05/Aug/2025:00:38:41 -0500] \"GET /imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926 HTTP/1.1\" 500 1551 63"}, {"timestamp": "2025-08-04T08:11:48", "ip": "**************", "method": "POST", "url": "/imtonline/ISO/WCOM_PPCBCEGTest.pl", "response_time": 10, "raw_line": "************** - - [04/Aug/2025:08:11:48 -0500] \"POST /imtonline/ISO/WCOM_PPCBCEGTest.pl HTTP/1.1\" 500 463 10"}, {"timestamp": "2025-08-04T08:09:39", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 607, "raw_line": "************** - - [04/Aug/2025:08:09:39 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-08-04T08:11:47", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 603, "raw_line": "************** - - [04/Aug/2025:08:11:47 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-08-04T08:13:15", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************** - - [04/Aug/2025:08:13:15 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-08-04T08:20:14", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************** - - [04/Aug/2025:08:20:14 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-08-04T08:27:47", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 605, "raw_line": "************** - - [04/Aug/2025:08:27:47 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-08-04T08:35:02", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 607, "raw_line": "************* - - [04/Aug/2025:08:35:02 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-08-04T08:47:37", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [04/Aug/2025:08:47:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T08:47:48", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2010&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:08:47:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2010&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T08:39:07", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 604, "raw_line": "************* - - [04/Aug/2025:08:39:07 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-08-04T08:50:33", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:08:50:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T08:52:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:08:52:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T08:44:15", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 606, "raw_line": "************** - - [04/Aug/2025:08:44:15 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-08-04T08:57:58", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:08:57:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T09:20:32", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4R9AE2215SC051030&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:09:20:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4R9AE2215SC051030&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T09:33:19", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/GL_Engine.pl", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:09:33:19 -0500] \"POST /imtonline/WCom/GL_Engine.pl HTTP/1.1\" 500 1667 0"}, {"timestamp": "2025-08-04T09:23:56", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 608, "raw_line": "************* - - [04/Aug/2025:09:23:56 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 608"}, {"timestamp": "2025-08-04T09:38:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56vbe1421sm677336&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:09:38:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56vbe1421sm677336&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T09:38:58", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:09:38:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T09:56:47", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [04/Aug/2025:09:56:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T10:01:17", "ip": "**************", "method": "POST", "url": "/imtonline/Claims/Claims_Engine.pl", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:10:01:17 -0500] \"POST /imtonline/Claims/Claims_Engine.pl HTTP/1.1\" 500 3899 0"}, {"timestamp": "2025-08-04T10:02:30", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:10:02:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T10:04:59", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:10:04:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T10:29:36", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [04/Aug/2025:10:29:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T10:29:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:10:29:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:00:52", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [04/Aug/2025:11:00:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T10:53:33", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 605, "raw_line": "************* - - [04/Aug/2025:10:53:33 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-08-04T11:04:34", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:11:04:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:04:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:04:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T10:56:44", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 602, "raw_line": "************* - - [04/Aug/2025:10:56:44 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 602"}, {"timestamp": "2025-08-04T11:10:05", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:10:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:17:53", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JLHF202X7G000102&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:17:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JLHF202X7G000102&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:18:20", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:18:20 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:25:44", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:25:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:28:42", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 603, "raw_line": "************* - - [04/Aug/2025:11:28:42 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-08-04T11:39:46", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:39:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:40:39", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:40:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:40:44", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:40:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:42:36", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1991&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:42:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1991&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:44:34", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:44:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:45:06", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:45:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:48:31", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:11:48:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:56:51", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5LEB1d525g1167020&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:56:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5LEB1d525g1167020&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T11:57:07", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:11:57:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T12:05:48", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:12:05:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T12:11:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:12:11:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T12:55:42", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:12:55:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T12:59:55", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:12:59:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:00:02", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:13:00:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:00:33", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:13:00:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:00:43", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:13:00:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:05:22", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:13:05:22 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:05:26", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:13:05:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:07:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUJACAS13LK97004&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:13:07:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUJACAS13LK97004&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:08:32", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:13:08:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:11:38", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:13:11:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:11:42", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:13:11:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:11:46", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:13:11:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:18:48", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [04/Aug/2025:13:18:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T13:44:44", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:13:44:44 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-08-04T13:57:45", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [04/Aug/2025:13:57:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T14:03:21", "ip": "*************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:14:03:21 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-04T14:20:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKADP9X4EJ391760&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:14:20:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKADP9X4EJ391760&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T14:21:11", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:14:21:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T14:21:20", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:14:21:20 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T14:29:30", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [04/Aug/2025:14:29:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T14:30:57", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:14:30:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T14:44:13", "ip": "*************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3415614&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2024-09-01&DEC_TYPE=3&origin=andapp", "response_time": 1, "raw_line": "************* - - [04/Aug/2025:14:44:13 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3415614&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2024-09-01&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 1"}, {"timestamp": "2025-08-04T14:59:44", "ip": "***********", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*********** - - [04/Aug/2025:14:59:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T15:27:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1991&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:15:27:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1991&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T16:17:08", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5jwtc1222sn614895&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:16:17:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5jwtc1222sn614895&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T16:17:59", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1222sN614895&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:16:17:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1222sN614895&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T16:20:19", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1222SN614895&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:16:20:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1222SN614895&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T16:27:29", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1222SN614895&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:16:27:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1222SN614895&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T16:54:00", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 605, "raw_line": "************ - - [04/Aug/2025:16:54:00 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-08-04T16:54:03", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 606, "raw_line": "************ - - [04/Aug/2025:16:54:03 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-08-04T17:48:58", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [04/Aug/2025:17:48:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T17:50:26", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:17:50:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T17:51:36", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [04/Aug/2025:17:51:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T17:52:34", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=550FP2227KS002304&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [04/Aug/2025:17:52:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=550FP2227KS002304&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T17:56:01", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [04/Aug/2025:17:56:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-04T17:56:53", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [04/Aug/2025:17:56:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T00:51:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [26/Jul/2025:00:51:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T00:53:25", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2004&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [26/Jul/2025:00:53:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2004&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T00:56:30", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [26/Jul/2025:00:56:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T00:56:47", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [26/Jul/2025:00:56:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T01:00:34", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [26/Jul/2025:01:00:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T01:07:32", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [26/Jul/2025:01:07:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T01:08:08", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [26/Jul/2025:01:08:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T01:08:52", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [26/Jul/2025:01:08:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-26T01:09:37", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [26/Jul/2025:01:09:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T06:51:45", "ip": "*************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10246828&date_processed=2025-07-25&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "************* - - [25/Jul/2025:06:51:45 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10246828&date_processed=2025-07-25&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-25T07:54:57", "ip": "**************", "method": "POST", "url": "/imtonline/smartreport/SREngine.pl", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:07:54:57 -0500] \"POST /imtonline/smartreport/SREngine.pl HTTP/1.1\" 500 1537 0"}, {"timestamp": "2025-07-25T07:55:00", "ip": "**************", "method": "POST", "url": "/imtonline/smartreport/SREngine.pl", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:07:55:00 -0500] \"POST /imtonline/smartreport/SREngine.pl HTTP/1.1\" 500 1537 0"}, {"timestamp": "2025-07-25T07:48:22", "ip": "*************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "response_time": 600, "raw_line": "************* - - [25/Jul/2025:07:48:22 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T07:49:42", "ip": "************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "response_time": 600, "raw_line": "************ - - [25/Jul/2025:07:49:42 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T07:52:50", "ip": "*************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_MyDocuments&origin=mpp", "response_time": 600, "raw_line": "************* - - [25/Jul/2025:07:52:50 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_MyDocuments&origin=mpp HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:17:00", "ip": "**************", "method": "GET", "url": "/login/reset.pl?forgotpass=1&str=JNPpigAKZk1ukTJWKiRA4Nb_6OTm9tvM2wZKoreK&email=<EMAIL>&username=ACBRUNO&origin=mppweb", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:08:17:00 -0500] \"GET /login/reset.pl?forgotpass=1&str=JNPpigAKZk1ukTJWKiRA4Nb_6OTm9tvM2wZKoreK&email=<EMAIL>&username=ACBRUNO&origin=mppweb HTTP/1.1\" 500 704 0"}, {"timestamp": "2025-07-25T08:17:36", "ip": "**************", "method": "POST", "url": "/imtonline/uassist/UAssistEngine.pl", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:08:17:36 -0500] \"POST /imtonline/uassist/UAssistEngine.pl HTTP/1.1\" 500 1474 0"}, {"timestamp": "2025-07-25T08:07:48", "ip": "**************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 601, "raw_line": "************** - - [25/Jul/2025:08:07:48 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 601"}, {"timestamp": "2025-07-25T08:08:50", "ip": "**************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "************** - - [25/Jul/2025:08:08:50 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:08:59", "ip": "**************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "************** - - [25/Jul/2025:08:08:59 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:09:37", "ip": "**************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "************** - - [25/Jul/2025:08:09:37 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:09:48", "ip": "***************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "*************** - - [25/Jul/2025:08:09:48 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:09:50", "ip": "*************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "response_time": 600, "raw_line": "************* - - [25/Jul/2025:08:09:50 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:11:27", "ip": "************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 602, "raw_line": "************ - - [25/Jul/2025:08:11:27 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 602"}, {"timestamp": "2025-07-25T08:21:06", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2565226", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:08:21:06 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2565226 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T08:23:40", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2325088", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:08:23:40 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2325088 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T08:27:57", "ip": "***************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 601, "raw_line": "*************** - - [25/Jul/2025:08:27:57 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 601"}, {"timestamp": "2025-07-25T08:29:06", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 608, "raw_line": "*************** - - [25/Jul/2025:08:29:06 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 608"}, {"timestamp": "2025-07-25T08:32:16", "ip": "***************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "*************** - - [25/Jul/2025:08:32:16 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:38:06", "ip": "************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "response_time": 600, "raw_line": "************ - - [25/Jul/2025:08:38:06 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:38:14", "ip": "************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "response_time": 602, "raw_line": "************ - - [25/Jul/2025:08:38:14 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing HTTP/1.1\" 500 532 602"}, {"timestamp": "2025-07-25T08:38:24", "ip": "***************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "*************** - - [25/Jul/2025:08:38:24 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:38:44", "ip": "************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing", "response_time": 600, "raw_line": "************ - - [25/Jul/2025:08:38:44 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl?load=IP_Billing HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:39:01", "ip": "***************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "*************** - - [25/Jul/2025:08:39:01 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:40:30", "ip": "*************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "************* - - [25/Jul/2025:08:40:30 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:40:48", "ip": "*************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 600, "raw_line": "************* - - [25/Jul/2025:08:40:48 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 600"}, {"timestamp": "2025-07-25T08:56:59", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:08:56:59 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T08:57:08", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:08:57:08 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T08:56:35", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 217, "raw_line": "************* - - [25/Jul/2025:08:56:35 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 217"}, {"timestamp": "2025-07-25T08:58:42", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 131, "raw_line": "************* - - [25/Jul/2025:08:58:42 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 131"}, {"timestamp": "2025-07-25T08:59:40", "ip": "************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************ - - [25/Jul/2025:08:59:40 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:01:34", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:01:34 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T08:59:37", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 312, "raw_line": "*************** - - [25/Jul/2025:08:59:37 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 312"}, {"timestamp": "2025-07-25T09:07:20", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:07:20 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:08:24", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:08:24 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:08:40", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 183, "raw_line": "************* - - [25/Jul/2025:09:08:40 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 183"}, {"timestamp": "2025-07-25T09:09:31", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:09:31 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:10:44", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2175145", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:10:44 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2175145 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:11:40", "ip": "***************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2615678", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:11:40 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2615678 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:12:24", "ip": "**************2", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2625766", "response_time": 180, "raw_line": "**************2 - - [25/Jul/2025:09:12:24 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2625766 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:12:43", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:12:43 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:14:30", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:14:30 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:15:55", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2259697", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:15:55 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2259697 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:16:17", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:16:17 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:17:30", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:17:30 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:21:25", "ip": "*************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2625772", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:21:25 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2625772 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:23:11", "ip": "*************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2625775", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:23:11 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2625775 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:24:28", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2175145", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:24:28 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2175145 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:24:36", "ip": "************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************ - - [25/Jul/2025:09:24:36 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:25:52", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:25:52 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:26:27", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:26:27 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:27:15", "ip": "*************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2625778", "response_time": 180, "raw_line": "************* - - [25/Jul/2025:09:27:15 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2625778 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:28:28", "ip": "**************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:28:28 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:29:30", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:09:29:30 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:30:48", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:09:30:48 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:31:46", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2175145", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:31:46 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2175145 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:31:52", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:09:31:52 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:32:09", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:09:32:09 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:33:03", "ip": "***************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2608385", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:33:03 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2608385 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:33:03", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:09:33:03 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:33:20", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "*************** - - [25/Jul/2025:09:33:20 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:33:32", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2259697", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:33:32 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2259697 HTTP/1.1\" 500 475 180"}, {"timestamp": "2025-07-25T09:34:55", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 131, "raw_line": "************* - - [25/Jul/2025:09:34:55 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 131"}, {"timestamp": "2025-07-25T09:34:10", "ip": "**************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 195, "raw_line": "************** - - [25/Jul/2025:09:34:10 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 195"}, {"timestamp": "2025-07-25T09:34:36", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 238, "raw_line": "************* - - [25/Jul/2025:09:34:36 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 238"}, {"timestamp": "2025-07-25T09:36:02", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "************* - - [25/Jul/2025:09:36:02 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:36:18", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 181, "raw_line": "************* - - [25/Jul/2025:09:36:18 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 181"}, {"timestamp": "2025-07-25T09:36:38", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 251, "raw_line": "************* - - [25/Jul/2025:09:36:38 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 251"}, {"timestamp": "2025-07-25T09:37:01", "ip": "************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 283, "raw_line": "************ - - [25/Jul/2025:09:37:01 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 283"}, {"timestamp": "2025-07-25T09:39:29", "ip": "**************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:39:29 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:40:38", "ip": "***************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2313300", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:40:38 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2313300 HTTP/1.1\" 500 455 180"}, {"timestamp": "2025-07-25T09:40:40", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2456960", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:40:40 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2456960 HTTP/1.1\" 500 455 180"}, {"timestamp": "2025-07-25T09:40:54", "ip": "**************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "************** - - [25/Jul/2025:09:40:54 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:42:54", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 352, "raw_line": "************* - - [25/Jul/2025:09:42:54 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 352"}, {"timestamp": "2025-07-25T09:54:42", "ip": "***************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 180, "raw_line": "*************** - - [25/Jul/2025:09:54:42 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 180"}, {"timestamp": "2025-07-25T09:55:06", "ip": "**************2", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2625766", "response_time": 180, "raw_line": "**************2 - - [25/Jul/2025:09:55:06 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2625766 HTTP/1.1\" 500 455 180"}, {"timestamp": "2025-07-25T10:06:22", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:06:22 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:07:13", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:07:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:09:40", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:09:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:10:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:10:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:08:18", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2541960", "response_time": 135, "raw_line": "************** - - [25/Jul/2025:10:08:18 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2541960 HTTP/1.1\" 500 377 135"}, {"timestamp": "2025-07-25T10:22:19", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:22:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:22:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:22:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:23:18", "ip": "**************", "method": "GET", "url": "/imtonline/PERS/HM/HM_Engine.pl?gid=2625415", "response_time": 2, "raw_line": "************** - - [25/Jul/2025:10:23:18 -0500] \"GET /imtonline/PERS/HM/HM_Engine.pl?gid=2625415 HTTP/1.1\" 500 517 2"}, {"timestamp": "2025-07-25T10:18:22", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 605, "raw_line": "************* - - [25/Jul/2025:10:18:22 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-25T10:28:46", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:28:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:30:04", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:30:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:31:38", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:31:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:33:42", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:33:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:34:37", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:10:34:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:35:40", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:10:35:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:36:17", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52WBU1215BR002332&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:36:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=52WBU1215BR002332&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:36:56", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [25/Jul/2025:10:36:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:37:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:37:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:38:17", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:10:38:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:30:20", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "*************** - - [25/Jul/2025:10:30:20 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-25T10:47:10", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5L3CX2024JL000053&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:47:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5L3CX2024JL000053&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:49:10", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:49:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:50:36", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:50:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:52:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5FTFA3730G1000597&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:52:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5FTFA3730G1000597&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:54:04", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:10:54:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T10:54:31", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2000&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:10:54:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2000&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:00:15", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:11:00:15 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:07:41", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:11:07:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:19:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:11:19:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:23:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1A9L64121EA245284&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:11:23:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1A9L64121EA245284&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:15:42", "ip": "***********", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 605, "raw_line": "*********** - - [25/Jul/2025:11:15:42 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-25T11:29:16", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:11:29:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:29:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:11:29:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T11:26:06", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 604, "raw_line": "************** - - [25/Jul/2025:11:26:06 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-25T11:48:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1K9GJ24443H048313&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:11:48:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1K9GJ24443H048313&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T12:01:09", "ip": "***************", "method": "POST", "url": "/imtonline/PERS/HM/HM_Engine.pl", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:12:01:09 -0500] \"POST /imtonline/PERS/HM/HM_Engine.pl HTTP/1.1\" 500 327 0"}, {"timestamp": "2025-07-25T12:26:28", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [25/Jul/2025:12:26:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T12:26:54", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:12:26:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T13:08:08", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:13:08:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T13:28:17", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:13:28:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T13:55:35", "ip": "************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3479798&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-10&DEC_TYPE=3&origin=andapp", "response_time": 1, "raw_line": "************ - - [25/Jul/2025:13:55:35 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3479798&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-10&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 1"}, {"timestamp": "2025-07-25T14:18:39", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 606, "raw_line": "************** - - [25/Jul/2025:14:18:39 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 - 606"}, {"timestamp": "2025-07-25T14:44:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:14:44:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T14:45:31", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:14:45:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T15:08:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUUS423GW060002&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:15:08:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUUS423GW060002&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T15:30:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUUS423GW060002&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [25/Jul/2025:15:30:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUUS423GW060002&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T15:31:30", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:15:31:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T16:00:47", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 605, "raw_line": "************ - - [25/Jul/2025:16:00:47 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-25T16:11:29", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [25/Jul/2025:16:11:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T16:16:28", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [25/Jul/2025:16:16:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-25T16:13:03", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 603, "raw_line": "************* - - [25/Jul/2025:16:13:03 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-25T16:20:31", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 606, "raw_line": "*************** - - [25/Jul/2025:16:20:31 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-24T07:56:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1K9GJ36691H048079&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:07:56:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1K9GJ36691H048079&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:01:47", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4YMCL12159M003558&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:08:01:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4YMCL12159M003558&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:04:00", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:08:04:00 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:05:09", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1k9gj36691h048079&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:08:05:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1k9gj36691h048079&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:07:13", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:08:07:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:09:12", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:08:09:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:11:23", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:08:11:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:13:20", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:08:13:20 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:15:34", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:08:15:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T08:59:15", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [24/Jul/2025:08:59:15 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-24T09:17:49", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [24/Jul/2025:09:17:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T09:23:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u01s14215a023100&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:09:23:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u01s14215a023100&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T09:38:17", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [24/Jul/2025:09:38:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T09:38:15", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 1, "raw_line": "************** - - [24/Jul/2025:09:38:15 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 1"}, {"timestamp": "2025-07-24T09:38:56", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:09:38:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T09:55:44", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:09:55:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T09:57:50", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [24/Jul/2025:09:57:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T09:57:59", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:09:57:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:10:52", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [24/Jul/2025:10:10:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:10:59", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:10:10:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:17:54", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EC25N1008393&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:10:17:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EC25N1008393&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:27:22", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:10:27:22 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:28:10", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:10:28:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:32:08", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [24/Jul/2025:10:32:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:32:09", "ip": "***************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:10:32:09 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-24T10:32:13", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:10:32:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:35:22", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:10:35:22 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T10:41:20", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:10:41:20 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T11:27:14", "ip": "**************7", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************7 - - [24/Jul/2025:11:27:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T11:41:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:11:41:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T11:38:38", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 605, "raw_line": "*************** - - [24/Jul/2025:11:38:38 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-24T11:50:50", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:11:50:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T11:44:37", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 607, "raw_line": "************** - - [24/Jul/2025:11:44:37 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 - 607"}, {"timestamp": "2025-07-24T11:56:09", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:11:56:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T12:03:36", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [24/Jul/2025:12:03:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T12:17:58", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:12:17:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T12:23:29", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:12:23:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T12:36:21", "ip": "**************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:12:36:21 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-24T13:10:47", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 605, "raw_line": "*************** - - [24/Jul/2025:13:10:47 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-24T13:31:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:31:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:33:26", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:33:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:35:16", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:35:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:36:06", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:36:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:37:35", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:37:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:37:45", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:37:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:38:13", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2013&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:38:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2013&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:39:26", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4X4TSEV21YN024704&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:39:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4X4TSEV21YN024704&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:42:31", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:13:42:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:42:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:42:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:46:12", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:46:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:51:57", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:51:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:52:08", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:52:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:52:18", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:52:18 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:52:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:52:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:55:52", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JMUS1627NS010975&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:55:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JMUS1627NS010975&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:57:28", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:57:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:57:40", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:57:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:59:04", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:13:59:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T13:59:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FVACXBSXDDFB9917&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:13:59:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FVACXBSXDDFB9917&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T14:22:01", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:14:22:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T15:15:24", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [24/Jul/2025:15:15:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T15:13:05", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 604, "raw_line": "*************** - - [24/Jul/2025:15:13:05 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-24T15:54:49", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7LZBE1621NW109801&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:15:54:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7LZBE1621NW109801&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T16:10:56", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u5cc1822ve00336a&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:16:10:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u5cc1822ve00336a&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T16:12:54", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4U5CC1822VE00336A&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [24/Jul/2025:16:12:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4U5CC1822VE00336A&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-24T18:34:02", "ip": "**************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************** - - [24/Jul/2025:18:34:02 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-23T08:37:33", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:08:37:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T08:41:11", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [23/Jul/2025:08:41:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T08:42:44", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:08:42:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T08:46:59", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=542BC1626FB011911&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:08:46:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=542BC1626FB011911&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T09:17:38", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "*************** - - [23/Jul/2025:09:17:38 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-23T09:24:55", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 614, "raw_line": "************** - - [23/Jul/2025:09:24:55 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 614"}, {"timestamp": "2025-07-23T09:44:50", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:09:44:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T09:47:54", "ip": "***************", "method": "POST", "url": "/imtonline/PERS/HM/HM_Engine.pl", "response_time": 3, "raw_line": "*************** - - [23/Jul/2025:09:47:54 -0500] \"POST /imtonline/PERS/HM/HM_Engine.pl HTTP/1.1\" 500 658 3"}, {"timestamp": "2025-07-23T09:53:08", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:09:53:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T09:59:10", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:09:59:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:06:37", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:10:06:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:07:04", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:10:07:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:07:27", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:10:07:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:08:07", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:10:08:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:08:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:10:08:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:15:40", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 2, "raw_line": "************** - - [23/Jul/2025:10:15:40 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 2"}, {"timestamp": "2025-07-23T10:17:47", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:10:17:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:18:01", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:10:18:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:09:34", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 606, "raw_line": "*************** - - [23/Jul/2025:10:09:34 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-23T10:20:54", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:10:20:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:20:59", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:10:20:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1998&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:29:48", "ip": "**************7", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************7 - - [23/Jul/2025:10:29:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:37:48", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:10:37:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:38:48", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:10:38:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:40:57", "ip": "**************7", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************7 - - [23/Jul/2025:10:40:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:42:17", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:10:42:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T10:42:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:10:42:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T11:05:52", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:11:05:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T11:37:37", "ip": "**************7", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************7 - - [23/Jul/2025:11:37:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T11:56:01", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 605, "raw_line": "*************** - - [23/Jul/2025:11:56:01 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-07-23T11:57:11", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "*************** - - [23/Jul/2025:11:57:11 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-23T11:59:18", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 603, "raw_line": "*************** - - [23/Jul/2025:11:59:18 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-23T12:25:52", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:12:25:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T12:27:50", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:12:27:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T12:40:05", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:12:40:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T12:44:32", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=58e1w1422s2019826&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:12:44:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=58e1w1422s2019826&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T13:04:54", "ip": "**************", "method": "POST", "url": "/imtonline/uassist/UAssistEngine.pl", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:13:04:54 -0500] \"POST /imtonline/uassist/UAssistEngine.pl HTTP/1.1\" 500 1206 0"}, {"timestamp": "2025-07-23T13:46:20", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:13:46:20 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T13:36:59", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 606, "raw_line": "*************** - - [23/Jul/2025:13:36:59 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-23T13:44:10", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************* - - [23/Jul/2025:13:44:10 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-23T13:44:54", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 617, "raw_line": "************* - - [23/Jul/2025:13:44:54 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 617"}, {"timestamp": "2025-07-23T14:08:58", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:14:08:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:09:03", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:14:09:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:10:02", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:14:10:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:10:45", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:14:10:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:10:50", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2013&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:14:10:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2013&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:11:23", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:14:11:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:11:27", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [23/Jul/2025:14:11:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:11:29", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2014&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:14:11:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2014&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:19:14", "ip": "*************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10491423&date_processed=2025-07-23&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "************* - - [23/Jul/2025:14:19:14 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10491423&date_processed=2025-07-23&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-23T14:26:06", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:14:26:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:27:42", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [23/Jul/2025:14:27:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T14:40:52", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=13SCH2422G1CA1228&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:14:40:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=13SCH2422G1CA1228&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T15:30:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:15:30:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1972&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T15:48:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3AKJGEDV1HSHZ5928&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:15:48:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3AKJGEDV1HSHZ5928&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T15:48:13", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3AKJGEDV5HSHZ5916&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:15:48:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3AKJGEDV5HSHZ5916&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T15:59:36", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:15:59:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T16:01:42", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56vbe1620gm628794&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:16:01:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56vbe1620gm628794&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T16:01:55", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2000&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [23/Jul/2025:16:01:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2000&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T16:05:23", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [23/Jul/2025:16:05:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-23T16:16:34", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 3, "raw_line": "************* - - [23/Jul/2025:16:16:34 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 3"}, {"timestamp": "2025-07-23T16:16:50", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 3, "raw_line": "************* - - [23/Jul/2025:16:16:50 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 3"}, {"timestamp": "2025-07-23T16:17:19", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 3, "raw_line": "************* - - [23/Jul/2025:16:17:19 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 3"}, {"timestamp": "2025-07-23T16:18:06", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 3, "raw_line": "************* - - [23/Jul/2025:16:18:06 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 3"}, {"timestamp": "2025-07-23T16:18:52", "ip": "*************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 3, "raw_line": "************* - - [23/Jul/2025:16:18:52 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 3"}, {"timestamp": "2025-07-22T06:07:03", "ip": "************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:06:07:03 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-22T06:09:04", "ip": "*************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:06:09:04 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-22T06:09:14", "ip": "************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:06:09:14 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10343887&date_processed=2025-07-22&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-22T06:22:03", "ip": "***************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:06:22:03 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T06:22:07", "ip": "***************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:06:22:07 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T06:24:11", "ip": "***************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:06:24:11 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T07:54:42", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:07:54:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T08:47:15", "ip": "***************", "method": "GET", "url": "/imtonline/uassist/UAssistEngine.pl?trans_id=2298443&load=UATransView&oldUnd=36&post=1&setNewUnd=1&newUnd=66", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:08:47:15 -0500] \"GET /imtonline/uassist/UAssistEngine.pl?trans_id=2298443&load=UATransView&oldUnd=36&post=1&setNewUnd=1&newUnd=66 HTTP/1.1\" 500 1474 0"}, {"timestamp": "2025-07-22T08:47:31", "ip": "***************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 2, "raw_line": "*************** - - [22/Jul/2025:08:47:31 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1334 2"}, {"timestamp": "2025-07-22T08:53:42", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:08:53:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T09:13:33", "ip": "*************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:09:13:33 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T09:30:13", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 607, "raw_line": "************* - - [22/Jul/2025:09:30:13 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-07-22T09:52:42", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:09:52:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T09:49:24", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "*************** - - [22/Jul/2025:09:49:24 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-22T10:03:26", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:10:03:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T09:54:45", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************ - - [22/Jul/2025:09:54:45 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-22T10:08:08", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4YDT33728LH939056&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:10:08:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4YDT33728LH939056&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:08:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE162XFM620068&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:10:08:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE162XFM620068&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:08:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7G1BE2025LE006865&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:10:08:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=7G1BE2025LE006865&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:08:44", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:10:08:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:10:04", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:10:10:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:15:52", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:10:15:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:17:25", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:10:17:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:28:39", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:10:28:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:30:09", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:10:30:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:37:55", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:10:37:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:41:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:10:41:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:49:03", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:10:49:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T10:49:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:10:49:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:01:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4p5de222041060220&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:11:01:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4p5de222041060220&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:01:32", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:11:01:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:07:35", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:11:07:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:08:31", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:11:08:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:13:59", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:11:13:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:21:33", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:11:21:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T11:28:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56ZL1UG26JP000095&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:11:28:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56ZL1UG26JP000095&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T12:14:04", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 607, "raw_line": "*************** - - [22/Jul/2025:12:14:04 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-07-22T12:16:10", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 603, "raw_line": "*************** - - [22/Jul/2025:12:16:10 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-22T12:50:06", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:12:50:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T12:52:27", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 603, "raw_line": "************* - - [22/Jul/2025:12:52:27 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-22T13:20:49", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:13:20:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T13:23:36", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:13:23:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T14:16:40", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:14:16:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T14:33:18", "ip": "**************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:14:33:18 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T14:33:23", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:14:33:23 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T14:51:56", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:14:51:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:02:27", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:15:02:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:02:33", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:15:02:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1996&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:02:49", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:02:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:20:01", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:15:20:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:21:45", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:21:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:22:57", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/CP_Engine.pl", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:15:22:57 -0500] \"POST /imtonline/WCom/CP_Engine.pl HTTP/1.1\" 500 685 0"}, {"timestamp": "2025-07-22T15:46:05", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:15:46:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:48:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1975&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:15:48:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1975&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:50:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:50:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:51:04", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:15:51:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:51:28", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [22/Jul/2025:15:51:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:52:28", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:15:52:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:52:30", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:52:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:53:54", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:53:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:55:05", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:55:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T15:56:25", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [22/Jul/2025:15:56:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:01:06", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1dgrs16237m074781&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:01:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1dgrs16237m074781&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:01:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1421SM677336&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:01:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE1421SM677336&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:28:18", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:28:18 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:30:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:30:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2012&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:34:58", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de27n1211340&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:34:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de27n1211340&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:35:08", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:35:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:44:39", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53bltea25ga021594&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:44:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53bltea25ga021594&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:44:51", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:44:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2016&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:48:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de24j1179182&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [22/Jul/2025:16:48:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5leb1de24j1179182&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T16:50:38", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 604, "raw_line": "************* - - [22/Jul/2025:16:50:38 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-22T17:11:28", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [22/Jul/2025:17:11:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-22T19:49:50", "ip": "***************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=ios", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:19:49:50 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=ios HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-22T20:01:25", "ip": "***************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp", "response_time": 0, "raw_line": "*************** - - [22/Jul/2025:20:01:25 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503220&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-08-03T09:22:21", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [03/Aug/2025:09:22:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-02T12:16:33", "ip": "**************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************** - - [02/Aug/2025:12:16:33 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-02T12:16:39", "ip": "**************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************** - - [02/Aug/2025:12:16:39 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-02T12:17:36", "ip": "**************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************** - - [02/Aug/2025:12:17:36 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-02T12:17:46", "ip": "**************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************** - - [02/Aug/2025:12:17:46 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-02T12:17:56", "ip": "**************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************** - - [02/Aug/2025:12:17:56 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-02T12:18:10", "ip": "**************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************** - - [02/Aug/2025:12:18:10 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-02T16:44:12", "ip": "*************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************* - - [02/Aug/2025:16:44:12 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-08-01T07:14:01", "ip": "**************3", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************3 - - [01/Aug/2025:07:14:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T07:47:33", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:07:47:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T09:04:34", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [01/Aug/2025:09:04:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T09:27:43", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:09:27:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T09:30:24", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************** - - [01/Aug/2025:09:30:24 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-08-01T09:48:35", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:09:48:35 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-08-01T10:10:45", "ip": "************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10413407&date_processed=2025-08-01&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:10:10:45 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10413407&date_processed=2025-08-01&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-08-01T10:16:24", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [01/Aug/2025:10:16:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T10:30:58", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:10:30:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T10:33:27", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 2, "raw_line": "************* - - [01/Aug/2025:10:33:27 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1362 2"}, {"timestamp": "2025-08-01T10:35:10", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 2, "raw_line": "************* - - [01/Aug/2025:10:35:10 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1362 2"}, {"timestamp": "2025-08-01T10:38:49", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 608, "raw_line": "************** - - [01/Aug/2025:10:38:49 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 608"}, {"timestamp": "2025-08-01T11:07:42", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:11:07:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T11:20:16", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [01/Aug/2025:11:20:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T11:20:33", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:11:20:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T11:27:20", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 605, "raw_line": "************** - - [01/Aug/2025:11:27:20 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 605"}, {"timestamp": "2025-08-01T11:42:52", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:11:42:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T12:40:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5HABE1226LN086327&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:12:40:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5HABE1226LN086327&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T12:40:20", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:12:40:20 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T12:43:05", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:12:43:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1995&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T12:43:53", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [01/Aug/2025:12:43:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T12:55:39", "ip": "************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:12:55:39 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-01T12:56:39", "ip": "*************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************* - - [01/Aug/2025:12:56:39 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-01T12:56:41", "ip": "************", "method": "POST", "url": "/imtonline/soap_service.pl", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:12:56:41 -0500] \"POST /imtonline/soap_service.pl HTTP/1.1\" 500 610 0"}, {"timestamp": "2025-08-01T13:31:48", "ip": "************", "method": "POST", "url": "/imtonline/Common/GAS/newAccountNumber.pl", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:13:31:48 -0500] \"POST /imtonline/Common/GAS/newAccountNumber.pl HTTP/1.1\" 500 310 0"}, {"timestamp": "2025-08-01T13:32:28", "ip": "**************", "method": "POST", "url": "/imtonline/Common/GAS/newAccountNumber.pl", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:13:32:28 -0500] \"POST /imtonline/Common/GAS/newAccountNumber.pl HTTP/1.1\" 500 310 0"}, {"timestamp": "2025-08-01T13:32:44", "ip": "**************", "method": "POST", "url": "/imtonline/Common/GAS/newAccountNumber.pl", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:13:32:44 -0500] \"POST /imtonline/Common/GAS/newAccountNumber.pl HTTP/1.1\" 500 310 0"}, {"timestamp": "2025-08-01T13:33:57", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [01/Aug/2025:13:33:57 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-08-01T13:34:32", "ip": "*************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************* - - [01/Aug/2025:13:34:32 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-08-01T13:39:46", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/website/TheIMTGroup/claims-download/csdownloads/downloadsList.php", "response_time": 10, "raw_line": "127.0.0.1 - - [01/Aug/2025:13:39:46 -0500] \"POST /imtonline/website/TheIMTGroup/claims-download/csdownloads/downloadsList.php HTTP/1.1\" 500 - 10"}, {"timestamp": "2025-08-01T13:39:46", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/website/TheIMTGroup/claims-download/csToken.php", "response_time": 10, "raw_line": "127.0.0.1 - - [01/Aug/2025:13:39:46 -0500] \"POST /imtonline/website/TheIMTGroup/claims-download/csToken.php HTTP/1.1\" 500 - 10"}, {"timestamp": "2025-08-01T14:09:37", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4J6EX24269B113381&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:14:09:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4J6EX24269B113381&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T14:09:55", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:14:09:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T14:25:14", "ip": "*************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 603, "raw_line": "************* - - [01/Aug/2025:14:25:14 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-08-01T15:45:07", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/document-management/upload/", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:15:45:07 -0500] \"POST /imtonline/api/v1/document-management/upload/ HTTP/1.1\" 500 909 0"}, {"timestamp": "2025-08-01T15:46:31", "ip": "***************", "method": "POST", "url": "/imtonline/smartreport/SREngine.pl", "response_time": 2, "raw_line": "*************** - - [01/Aug/2025:15:46:31 -0500] \"POST /imtonline/smartreport/SREngine.pl HTTP/1.1\" 500 3291 2"}, {"timestamp": "2025-08-01T15:59:24", "ip": "***************", "method": "POST", "url": "/imtonline/smartreport/SREngine.pl", "response_time": 2, "raw_line": "*************** - - [01/Aug/2025:15:59:24 -0500] \"POST /imtonline/smartreport/SREngine.pl HTTP/1.1\" 500 3047 2"}, {"timestamp": "2025-08-01T16:34:43", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:16:34:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T16:32:23", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 603, "raw_line": "*************** - - [01/Aug/2025:16:32:23 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-08-01T16:42:45", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [01/Aug/2025:16:42:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T16:42:52", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:16:42:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2009&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T18:08:33", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [01/Aug/2025:18:08:33 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-08-01T18:08:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [01/Aug/2025:18:08:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T00:02:19", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:00:02:19 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1411 0"}, {"timestamp": "2025-07-31T00:29:01", "ip": "**************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 741, "raw_line": "************** - - [31/Jul/2025:00:29:01 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 532 741"}, {"timestamp": "2025-07-31T07:03:37", "ip": "**************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503188&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2024-10-28&DEC_TYPE=3&origin=andapp", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:07:03:37 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3503188&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2024-10-28&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T08:19:48", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:08:19:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T08:25:39", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 1, "raw_line": "************** - - [31/Jul/2025:08:25:39 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 1"}, {"timestamp": "2025-07-31T08:38:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:08:38:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T08:48:57", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u5cc1822ve00336a&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:08:48:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4u5cc1822ve00336a&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T08:49:01", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:08:49:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T08:49:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:08:49:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T08:52:28", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 5, "raw_line": "************* - - [31/Jul/2025:08:52:28 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1285 5"}, {"timestamp": "2025-07-31T09:02:05", "ip": "*************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3537859&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-05-20&DEC_TYPE=3&origin=ios", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:09:02:05 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3537859&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-05-20&DEC_TYPE=3&origin=ios HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T09:27:54", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:09:27:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T09:32:31", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:09:32:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T09:39:51", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 16, "raw_line": "************** - - [31/Jul/2025:09:39:51 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 1347 16"}, {"timestamp": "2025-07-31T10:10:53", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:10:10:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:11:40", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:11:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:13:53", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=430UN1410PM075103&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:13:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=430UN1410PM075103&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:16:41", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:16:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:20:43", "ip": "**************3", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************3 - - [31/Jul/2025:10:20:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:21:13", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:21:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:22:47", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:22:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:24:01", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:24:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:24:09", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:24:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:25:32", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:25:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:27:41", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:27:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:30:41", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:30:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:33:17", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4TCSU110X5HW10792&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:33:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4TCSU110X5HW10792&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:41:42", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4X4TSE4162N025329&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:41:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4X4TSE4162N025329&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:41:50", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2002&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:10:41:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2002&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T10:43:10", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:10:43:10 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1284 0"}, {"timestamp": "2025-07-31T11:05:57", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:11:05:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:09:08", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSCEAHR75C199353&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:11:09:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSCEAHR75C199353&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:10:23", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:11:10:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:10:48", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2011&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:11:10:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2011&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:25:55", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:11:25:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:32:03", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWUP2029RN597203&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:11:32:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWUP2029RN597203&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:32:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:11:32:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T11:58:35", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:11:58:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T12:50:37", "ip": "*************", "method": "GET", "url": "/imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:12:50:37 -0500] \"GET /imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T12:58:29", "ip": "*************", "method": "GET", "url": "/imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:12:58:29 -0500] \"GET /imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T12:58:29", "ip": "*************", "method": "GET", "url": "/imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:12:58:29 -0500] \"GET /imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T12:58:30", "ip": "*************", "method": "GET", "url": "/imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:12:58:30 -0500] \"GET /imtonline/FarmLB/appFile.pl?sessionID=64765517.2114359 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T13:04:51", "ip": "**************0", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************0 - - [31/Jul/2025:13:04:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T13:28:26", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1967&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:13:28:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1967&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T13:36:50", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:13:36:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T13:41:01", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:13:41:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T13:56:49", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:13:56:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T13:56:55", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1988&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:13:56:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1988&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:04:50", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:04:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:04:58", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:04:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:09:35", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1988&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:09:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1988&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:12:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1GDM7H1C62J514898&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:12:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1GDM7H1C62J514898&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:12:18", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHULEV25BN071245&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:12:18 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHULEV25BN071245&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:17:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:17:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:17:56", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:17:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:19:23", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:19:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:22:07", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:22:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:23:09", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:23:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:26:12", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:26:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:26:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:26:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:41:01", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1D9FB1222GA509419&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:41:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1D9FB1222GA509419&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:42:59", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:42:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:44:34", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:44:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:45:02", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:45:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:47:11", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:14:47:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T14:47:18", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:14:47:18 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T15:49:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:15:49:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T15:54:46", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 1, "raw_line": "************** - - [31/Jul/2025:15:54:46 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 1"}, {"timestamp": "2025-07-31T15:56:13", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [31/Jul/2025:15:56:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T15:56:27", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:15:56:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2007&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T16:01:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [31/Jul/2025:16:01:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T16:27:06", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [31/Jul/2025:16:27:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T16:30:59", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:16:30:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-31T22:54:30", "ip": "************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3557685&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-29&DEC_TYPE=3", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:22:54:30 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3557685&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-29&DEC_TYPE=3 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T22:54:38", "ip": "************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3557685&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-29&DEC_TYPE=3", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:22:54:38 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3557685&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-29&DEC_TYPE=3 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-31T22:55:35", "ip": "************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3557685&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-29&DEC_TYPE=3&origin=andapp", "response_time": 0, "raw_line": "************ - - [31/Jul/2025:22:55:35 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3557685&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-07-29&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-30T00:14:40", "ip": "***************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 654, "raw_line": "*************** - - [30/Jul/2025:00:14:40 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 654"}, {"timestamp": "2025-07-30T00:15:05", "ip": "***************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 631, "raw_line": "*************** - - [30/Jul/2025:00:15:05 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 631"}, {"timestamp": "2025-07-30T00:14:56", "ip": "***************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 641, "raw_line": "*************** - - [30/Jul/2025:00:14:56 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 641"}, {"timestamp": "2025-07-30T00:14:42", "ip": "***************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 655, "raw_line": "*************** - - [30/Jul/2025:00:14:42 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 655"}, {"timestamp": "2025-07-30T00:16:14", "ip": "**************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 619, "raw_line": "************** - - [30/Jul/2025:00:16:14 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 619"}, {"timestamp": "2025-07-30T00:16:22", "ip": "**************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 620, "raw_line": "************** - - [30/Jul/2025:00:16:22 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 620"}, {"timestamp": "2025-07-30T00:16:53", "ip": "**************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 622, "raw_line": "************** - - [30/Jul/2025:00:16:53 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 622"}, {"timestamp": "2025-07-30T00:16:52", "ip": "**************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 623, "raw_line": "************** - - [30/Jul/2025:00:16:52 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 623"}, {"timestamp": "2025-07-30T00:19:05", "ip": "**************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 619, "raw_line": "************** - - [30/Jul/2025:00:19:05 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 619"}, {"timestamp": "2025-07-30T00:19:04", "ip": "**************", "method": "GET", "url": "/imtonline/Claims/Claims_Tracker.pl", "response_time": 620, "raw_line": "************** - - [30/Jul/2025:00:19:04 -0500] \"GET /imtonline/Claims/Claims_Tracker.pl HTTP/1.1\" 500 532 620"}, {"timestamp": "2025-07-30T02:56:29", "ip": "***********", "method": "GET", "url": "/imtonline/WadenaPAS/wa_xrf_Roadside.pl?vehNum=all&fromclient=1", "response_time": 0, "raw_line": "*********** - - [30/Jul/2025:02:56:29 -0500] \"GET /imtonline/WadenaPAS/wa_xrf_Roadside.pl?vehNum=all&fromclient=1 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-30T08:01:23", "ip": "**************", "method": "POST", "url": "/imtonline/uassist/UAssistEngine.pl", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:08:01:23 -0500] \"POST /imtonline/uassist/UAssistEngine.pl HTTP/1.1\" 500 1206 0"}, {"timestamp": "2025-07-30T08:38:23", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:08:38:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T08:39:11", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:08:39:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T08:29:44", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 607, "raw_line": "*************** - - [30/Jul/2025:08:29:44 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-07-30T08:40:04", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:08:40:04 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T08:40:53", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:08:40:53 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T08:41:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC2020PN576406&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:08:41:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC2020PN576406&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T08:56:07", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 1, "raw_line": "************** - - [30/Jul/2025:08:56:07 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 1"}, {"timestamp": "2025-07-30T09:09:49", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/document-management/upload/", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:09:09:49 -0500] \"POST /imtonline/api/v1/document-management/upload/ HTTP/1.1\" 500 973 0"}, {"timestamp": "2025-07-30T09:09:51", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/document-management/upload/process/", "response_time": 1, "raw_line": "************** - - [30/Jul/2025:09:09:51 -0500] \"POST /imtonline/api/v1/document-management/upload/process/ HTTP/1.1\" 500 - 1"}, {"timestamp": "2025-07-30T09:11:31", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/document-management/upload/process/", "response_time": 1, "raw_line": "************ - - [30/Jul/2025:09:11:31 -0500] \"POST /imtonline/api/v1/document-management/upload/process/ HTTP/1.1\" 500 - 1"}, {"timestamp": "2025-07-30T09:16:35", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [30/Jul/2025:09:16:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:17:50", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [30/Jul/2025:09:17:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:18:16", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [30/Jul/2025:09:18:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:18:35", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:09:18:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:31:41", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:09:31:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:36:48", "ip": "**************", "method": "POST", "url": "/imtonline/uassist/UAssistEngine.pl", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:09:36:48 -0500] \"POST /imtonline/uassist/UAssistEngine.pl HTTP/1.1\" 500 1474 0"}, {"timestamp": "2025-07-30T09:49:24", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:09:49:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:50:57", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:09:50:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T09:59:02", "ip": "***********", "method": "GET", "url": "/imtonline/WadenaPAS/wa_xrf_Roadside.pl?vehNum=all&fromclient=1", "response_time": 0, "raw_line": "*********** - - [30/Jul/2025:09:59:02 -0500] \"GET /imtonline/WadenaPAS/wa_xrf_Roadside.pl?vehNum=all&fromclient=1 HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-30T10:23:08", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:10:23:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T10:59:26", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [30/Jul/2025:10:59:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:10:07", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:11:10:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:15:31", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2M2N179Y1EC088864&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:15:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2M2N179Y1EC088864&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:22:46", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:11:22:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:22:50", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:22:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2003&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:26:59", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 2, "raw_line": "************** - - [30/Jul/2025:11:26:59 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 2"}, {"timestamp": "2025-07-30T11:33:22", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 1, "raw_line": "************** - - [30/Jul/2025:11:33:22 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 1"}, {"timestamp": "2025-07-30T11:36:57", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X02J884839&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:36:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X02J884839&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:36:58", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWD49XXPR256419&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:36:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWD49XXPR256419&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:37:00", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X71J882746&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:37:00 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X71J882746&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:37:01", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWD49X7GR103903&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:37:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWD49X7GR103903&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:37:03", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X14R061647&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:37:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKWDB9X14R061647&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:49:11", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [30/Jul/2025:11:49:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:50:03", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [30/Jul/2025:11:50:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:54:27", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1e9bf3220es230494&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:54:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1e9bf3220es230494&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T11:54:32", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2014&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:11:54:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2014&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T12:05:32", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:12:05:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T13:09:40", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:13:09:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T13:20:43", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:13:20:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T13:39:32", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:13:39:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T13:42:37", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:13:42:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T14:26:35", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:14:26:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T14:28:35", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:14:28:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T14:41:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:14:41:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:30:30", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:15:30:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:32:41", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:15:32:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:34:11", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:15:34:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:34:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:15:34:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:45:14", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:15:45:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:45:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53BLTEA26FF011044&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:15:45:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53BLTEA26FF011044&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:45:51", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:15:45:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:48:08", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:15:48:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:48:11", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [30/Jul/2025:15:48:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:56:00", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3GJ2FD1H9RM001492&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:15:56:00 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=3GJ2FD1H9RM001492&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:56:01", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:15:56:01 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T15:56:32", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:15:56:32 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T16:00:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:16:00:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5JWTC1627JN504479&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T16:17:34", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1YGUS1210EB110129&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:16:17:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1YGUS1210EB110129&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T17:28:15", "ip": "*************", "method": "POST", "url": "/imtonline/PERS/HM/HM_Engine.pl", "response_time": 0, "raw_line": "************* - - [30/Jul/2025:17:28:15 -0500] \"POST /imtonline/PERS/HM/HM_Engine.pl HTTP/1.1\" 500 357 0"}, {"timestamp": "2025-07-30T20:26:48", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FVACXFC0NHNM7630&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:26:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FVACXFC0NHNM7630&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:26:49", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=46UFU2225N1257835&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:26:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=46UFU2225N1257835&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:26:55", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=46UDE2226S1293467&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:26:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=46UDE2226S1293467&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:26:56", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U618224N383113&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:26:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U618224N383113&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:27:06", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U62229GN383001&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:27:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U62229GN383001&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:27:09", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=46UFU2222P1271971&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:27:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=46UFU2222P1271971&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:27:10", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U61825RN383310&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:27:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U61825RN383310&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:27:19", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U62227BN383006&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:27:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1S9U62227BN383006&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:37:10", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:20:37:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T20:38:42", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [30/Jul/2025:20:38:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-30T22:59:27", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:22:59:27 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-30T22:59:36", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [30/Jul/2025:22:59:36 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-29T07:09:09", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 1, "raw_line": "************** - - [29/Jul/2025:07:09:09 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1334 1"}, {"timestamp": "2025-07-29T08:11:09", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VMBED29SM681045&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:08:11:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VMBED29SM681045&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:11:31", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VMBED29SM681045&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:08:11:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VMBED29SM681045&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:44:03", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4ZECH1623R1325815&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:08:44:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4ZECH1623R1325815&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:51:40", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4PGBJ18196L027209&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:08:51:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4PGBJ18196L027209&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:51:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=430FD1622PM075279&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:08:51:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=430FD1622PM075279&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:52:16", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [29/Jul/2025:08:52:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:55:54", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:08:55:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2005&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T08:56:29", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/document-management/upload/", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:08:56:29 -0500] \"POST /imtonline/api/v1/document-management/upload/ HTTP/1.1\" 500 1091 0"}, {"timestamp": "2025-07-29T09:09:07", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:09:09:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1999&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:32:03", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:09:32:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:34:19", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:09:34:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:34:35", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:09:34:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:35:55", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:09:35:55 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:36:25", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:09:36:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:37:36", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:09:37:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:42:36", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:09:42:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T09:34:16", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 608, "raw_line": "*************** - - [29/Jul/2025:09:34:16 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 608"}, {"timestamp": "2025-07-29T09:53:29", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 6, "raw_line": "************** - - [29/Jul/2025:09:53:29 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 6"}, {"timestamp": "2025-07-29T09:45:21", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 606, "raw_line": "*************** - - [29/Jul/2025:09:45:21 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-29T09:47:22", "ip": "***********", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 607, "raw_line": "*********** - - [29/Jul/2025:09:47:22 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 607"}, {"timestamp": "2025-07-29T10:07:56", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:10:07:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T10:17:31", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=50XBE2420SA049619&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:10:17:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=50XBE2420SA049619&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T10:19:36", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************ - - [29/Jul/2025:10:19:36 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-29T10:25:52", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 606, "raw_line": "************ - - [29/Jul/2025:10:25:52 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-29T10:43:28", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKBD59XXLJ551128&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:10:43:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XKBD59XXLJ551128&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T10:49:05", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:10:49:05 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T11:01:39", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:11:01:39 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T11:12:40", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:11:12:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T11:42:10", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:11:42:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T11:56:50", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:11:56:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T12:02:28", "ip": "***************", "method": "GET", "url": "/imtonline/insuredPortal/insuredPortal.pl", "response_time": 32, "raw_line": "*************** - - [29/Jul/2025:12:02:28 -0500] \"GET /imtonline/insuredPortal/insuredPortal.pl HTTP/1.1\" 500 4633 32"}, {"timestamp": "2025-07-29T12:11:44", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:12:11:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T12:44:12", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:12:44:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T12:48:22", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:12:48:22 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T12:56:34", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WMBE182XR1008816&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:12:56:34 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WMBE182XR1008816&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T12:56:43", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:12:56:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:07:24", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:13:07:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:07:46", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:13:07:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:24:49", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WMBE182XR1008816&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:13:24:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5WMBE182XR1008816&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:26:29", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUAMH30EY020064&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:13:26:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5NHUAMH30EY020064&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:27:23", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:13:27:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:27:29", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:13:27:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:33:11", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:13:33:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:33:19", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:13:33:19 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2019&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:35:47", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:13:35:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:36:59", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:13:36:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:53:26", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:13:53:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T13:53:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:13:53:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2001&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:13:40", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:14:13:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:14:07", "ip": "***************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:14:14:07 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 1329 0"}, {"timestamp": "2025-07-29T14:14:10", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:14:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=1997&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:33:16", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:33:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2021&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:35:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:35:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:35:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:35:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:35:30", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:35:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:37:12", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:37:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:37:43", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:37:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:37:51", "ip": "*************", "method": "POST", "url": "/imtonline/WCom/GL_Engine.pl", "response_time": 65, "raw_line": "************* - - [29/Jul/2025:14:37:51 -0500] \"POST /imtonline/WCom/GL_Engine.pl HTTP/1.1\" 500 1617 65"}, {"timestamp": "2025-07-29T14:41:17", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:41:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:41:44", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:41:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:42:14", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:42:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:42:40", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:42:40 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:42:49", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LWB2824XP053169&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:42:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LWB2824XP053169&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:43:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:43:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:43:25", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:43:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:43:49", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:43:49 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:44:14", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:44:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:44:21", "ip": "**************8", "method": "GET", "url": "/imtonline/WC/WCEngine.pl?whereAreYouGoing=WC_print_main;func=WC_check_for_stored_pdf;sessionID=1952822.141266", "response_time": 5, "raw_line": "**************8 - - [29/Jul/2025:14:44:21 -0500] \"GET /imtonline/WC/WCEngine.pl?whereAreYouGoing=WC_print_main;func=WC_check_for_stored_pdf;sessionID=1952822.141266 HTTP/1.1\" 500 16428 5"}, {"timestamp": "2025-07-29T14:44:29", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:44:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:44:37", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:44:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2022&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:44:37", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:44:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:44:52", "ip": "**************8", "method": "GET", "url": "/imtonline/WC/WCEngine.pl?whereAreYouGoing=WC_print_main;func=WC_check_for_stored_pdf;sessionID=1952822.141266", "response_time": 6, "raw_line": "**************8 - - [29/Jul/2025:14:44:52 -0500] \"GET /imtonline/WC/WCEngine.pl?whereAreYouGoing=WC_print_main;func=WC_check_for_stored_pdf;sessionID=1952822.141266 HTTP/1.1\" 500 16428 6"}, {"timestamp": "2025-07-29T14:44:59", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:44:59 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:46:06", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:46:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:46:54", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:46:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:47:09", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:47:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:47:17", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:47:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:47:45", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:47:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:48:07", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:48:07 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:48:42", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:48:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:49:06", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:49:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:49:13", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:49:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:49:26", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:49:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:54:21", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:14:54:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:54:42", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:54:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:55:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2023&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:55:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2023&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:58:29", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:14:58:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:59:16", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [29/Jul/2025:14:59:16 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T14:59:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:14:59:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:00:52", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:15:00:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:00:58", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:15:00:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:03:52", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:15:03:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:04:50", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:15:04:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2024&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:07:03", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:15:07:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:07:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:15:07:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:06:40", "ip": "************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 604, "raw_line": "************ - - [29/Jul/2025:15:06:40 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-29T15:07:16", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 608, "raw_line": "************** - - [29/Jul/2025:15:07:16 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 608"}, {"timestamp": "2025-07-29T15:07:16", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 608, "raw_line": "************** - - [29/Jul/2025:15:07:16 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 608"}, {"timestamp": "2025-07-29T15:29:58", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:15:29:58 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T15:32:44", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 606, "raw_line": "*************** - - [29/Jul/2025:15:32:44 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 606"}, {"timestamp": "2025-07-29T15:58:10", "ip": "***********", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*********** - - [29/Jul/2025:15:58:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T16:26:03", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5nhuamh30ey020064&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:16:26:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5nhuamh30ey020064&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T16:30:42", "ip": "**************", "method": "POST", "url": "/imtonline/HO_HO0546.pl", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:16:30:42 -0500] \"POST /imtonline/HO_HO0546.pl HTTP/1.1\" 500 1868 0"}, {"timestamp": "2025-07-29T16:39:31", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [29/Jul/2025:16:39:31 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T18:44:17", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSCESBR67C504438&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:18:44:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSCESBR67C504438&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T18:44:22", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1HSDJAPR7FH743085&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:18:44:22 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1HSDJAPR7FH743085&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T18:44:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LWB262X8P147982&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:18:44:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=40LWB262X8P147982&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T18:44:29", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1HSHBAAN3SH692456&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:18:44:29 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1HSHBAAN3SH692456&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T18:46:24", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:18:46:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T18:52:09", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [29/Jul/2025:18:52:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-29T19:07:57", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [29/Jul/2025:19:07:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2020&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T07:29:36", "ip": "***************", "method": "GET", "url": "/imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10437133&date_processed=2025-07-25&dec_type=B&doc_group=inv", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:07:29:36 -0500] \"GET /imtonline/platform_legacy_integration/v1/policy_print/get_single_aws_key/?account_number=A10437133&date_processed=2025-07-25&dec_type=B&doc_group=inv HTTP/1.1\" 500 85 0"}, {"timestamp": "2025-07-28T08:33:51", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:08:33:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T08:38:05", "ip": "************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 5, "raw_line": "************ - - [28/Jul/2025:08:38:05 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 5"}, {"timestamp": "2025-07-28T08:39:13", "ip": "************", "method": "POST", "url": "/one_time_payment.pl", "response_time": 5, "raw_line": "************ - - [28/Jul/2025:08:39:13 -0500] \"POST /one_time_payment.pl HTTP/1.1\" 500 532 5"}, {"timestamp": "2025-07-28T09:24:50", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:09:24:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T09:54:28", "ip": "************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************ - - [28/Jul/2025:09:54:28 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:03:02", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:10:03:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:04:26", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:10:04:26 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:21:08", "ip": "**************3", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "**************3 - - [28/Jul/2025:10:21:08 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:36:47", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/CV_Engine.pl", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:10:36:47 -0500] \"POST /imtonline/WCom/CV_Engine.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-28T10:47:51", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JHBT23295D000224&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:10:47:51 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JHBT23295D000224&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:47:54", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JHBT23295D000224&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:10:47:54 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4JHBT23295D000224&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:49:02", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:10:49:02 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T10:49:14", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:10:49:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:18:50", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:11:18:50 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:20:06", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUYDSEB9VP823563&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:11:20:06 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1FUYDSEB9VP823563&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:39:56", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:11:39:56 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:40:17", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:11:40:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2018&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:42:43", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:11:42:43 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:47:47", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:11:47:47 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T11:51:24", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:11:51:24 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:06:30", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:06:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:08:03", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:08:03 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:18:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:18:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:10:08", "ip": "**************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account=", "response_time": 603, "raw_line": "************** - - [28/Jul/2025:12:10:08 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary&section=summary&account= HTTP/1.1\" 500 532 603"}, {"timestamp": "2025-07-28T12:23:23", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:23:23 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:23:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:23:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:27:36", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:27:36 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D24S1057397&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T12:29:12", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:12:29:12 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2J91F4D22S1057396&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T13:11:14", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:13:11:14 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:15:24", "ip": "*************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=WHM2L5V&ACTG1=1&LINE_OF_BUSINESS1=HO&POLICY_EFF_DATE1=2025-07-07&DEC_TYPE=3&origin=andapp", "response_time": 1, "raw_line": "************* - - [28/Jul/2025:14:15:24 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=WHM2L5V&ACTG1=1&LINE_OF_BUSINESS1=HO&POLICY_EFF_DATE1=2025-07-07&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 1"}, {"timestamp": "2025-07-28T14:15:31", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 604, "raw_line": "*************** - - [28/Jul/2025:14:15:31 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-28T14:29:29", "ip": "**************", "method": "POST", "url": "/imtonline/WCom/WBOP.pl", "response_time": 2, "raw_line": "************** - - [28/Jul/2025:14:29:29 -0500] \"POST /imtonline/WCom/WBOP.pl HTTP/1.1\" 500 16484 2"}, {"timestamp": "2025-07-28T14:32:38", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4VRHD2028XM006523&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:14:32:38 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=4VRHD2028XM006523&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:32:41", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XPHDU9X97N683830&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:14:32:41 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XPHDU9X97N683830&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:32:42", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5VGFR3625FL004418&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:14:32:42 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5VGFR3625FL004418&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:32:44", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSFHDPR6PC062494&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:14:32:44 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSFHDPR6PC062494&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:32:45", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSFHAER1XC024057&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:14:32:45 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=2HSFHAER1XC024057&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:32:46", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XPWD49X9DD196037&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:14:32:46 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1XPWD49X9DD196037&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T14:45:27", "ip": "*************", "method": "POST", "url": "/imtonline/PERS/HM/HM_Engine.pl", "response_time": 3, "raw_line": "************* - - [28/Jul/2025:14:45:27 -0500] \"POST /imtonline/PERS/HM/HM_Engine.pl HTTP/1.1\" 500 658 3"}, {"timestamp": "2025-07-28T15:12:25", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EB26K1003250&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:12:25 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=55ZR1EB26K1003250&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:13:17", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533SC1219FC249509&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:13:17 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533SC1219FC249509&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:13:27", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=76", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:13:27 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2015&action=2&class=76 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:20:11", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53MDX1423DB002729&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:20:11 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=53MDX1423DB002729&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:21:48", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE2920DM601463&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:21:48 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=56VBE2920DM601463&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:27:35", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:15:27:35 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:28:52", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:28:52 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiSearch.php?search=2025&action=2&class=2 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:32:21", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1G9CA453XPS139027&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:15:32:21 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=1G9CA453XPS139027&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:32:39", "ip": "************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************ - - [28/Jul/2025:15:32:39 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-28T15:32:58", "ip": "**************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:15:32:58 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-28T16:00:10", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533SC1215EC232172&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:16:00:10 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=533SC1215EC232172&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T16:02:13", "ip": "***************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:16:02:13 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T15:53:05", "ip": "***************", "method": "GET", "url": "/imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary", "response_time": 604, "raw_line": "*************** - - [28/Jul/2025:15:53:05 -0500] \"GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1\" 500 532 604"}, {"timestamp": "2025-07-28T16:16:30", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5ERBU0816EM075854&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:16:16:30 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5ERBU0816EM075854&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T16:16:37", "ip": "127.0.0.1", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5KGBBTF29E1012297&action=1", "response_time": 0, "raw_line": "127.0.0.1 - - [28/Jul/2025:16:16:37 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php?search=5KGBBTF29E1012297&action=1 HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T17:33:09", "ip": "**************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:17:33:09 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-28T21:44:45", "ip": "***************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "*************** - - [28/Jul/2025:21:44:45 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-28T21:58:49", "ip": "**************", "method": "POST", "url": "/imtonline/insuredPortal/mobileAppLogIn.pl", "response_time": 0, "raw_line": "************** - - [28/Jul/2025:21:58:49 -0500] \"POST /imtonline/insuredPortal/mobileAppLogIn.pl HTTP/1.1\" 500 532 0"}, {"timestamp": "2025-07-27T14:18:57", "ip": "*************", "method": "POST", "url": "/imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php", "response_time": 0, "raw_line": "************* - - [27/Jul/2025:14:18:57 -0500] \"POST /imtonline/api/v1/vehicleInfoAPI/apiVinSearch.php HTTP/1.1\" 500 - 0"}, {"timestamp": "2025-07-27T23:27:22", "ip": "************", "method": "GET", "url": "/agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3246326&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp", "response_time": 1, "raw_line": "************ - - [27/Jul/2025:23:27:22 -0500] \"GET /agent_dashboard/policy-center/pd_mobiuspdf.pl?TOPIC1=3246326&ACTG1=1&LINE_OF_BUSINESS1=WA&POLICY_EFF_DATE1=2025-03-23&DEC_TYPE=3&origin=andapp HTTP/1.1\" 500 532 1"}], "correlation_timeline": {}, "top_error_patterns": {}, "affected_scripts": {}}