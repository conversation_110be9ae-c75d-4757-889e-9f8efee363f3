{"summary": {"total_500_errors": 1, "total_error_log_entries": 94185, "successful_correlations": 1, "correlation_rate": 100.0, "avg_correlation_score": 79.**************, "correlation_methods": {"time_match(0.0s)+ip_match": 1}}, "correlations": [{"access_log": {"timestamp": "2025-08-05T00:38:41", "ip": "*************", "method": "GET", "url": "/imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926", "response_time": 63, "raw_line": "************* - - [05/Aug/2025:00:38:41 -0500] \"GET /imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926 HTTP/1.1\" 500 1551 63"}, "error_logs": [{"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:40 2025] client_sync_common.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.003362 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] client_sync_common.pm: \"my\" variable $modified_date masks earlier declaration in same scope at /imtonline/Common/Platform/client_sync_common.pm line 885.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:40 2025] client_sync_common.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.003405 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] client_sync_common.pm: \"my\" variable $version_type masks earlier declaration in same scope at /imtonline/Common/Platform/client_sync_common.pm line 887.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:40 2025] client_sync_common.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.003430 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] client_sync_common.pm: \"my\" variable $status masks earlier declaration in same scope at /imtonline/Common/Platform/client_sync_common.pm line 887.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[<PERSON><PERSON> Aug  5 00:38:41 2025] ClientEngine.pl", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.036447 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] ClientEngine.pl: Client::ClientEngine::decode_json() called too early to check prototype at /imtonline/Client/ClientEngine.pl line 95.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[<PERSON><PERSON> Aug  5 00:38:41 2025] ClientEngine.pl", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.041563 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] ClientEngine.pl: Use of uninitialized value in string ne at /imtonline/Client/ClientEngine.pl line 93.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[<PERSON><PERSON> Aug  5 00:38:41 2025] ClientEngine.pl", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.042601 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] ClientEngine.pl: Use of uninitialized value in string eq at /imtonline/Client/ClientEngine.pl line 162.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] Connect.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.717274 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::carp redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] Connect.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.717314 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::confess redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] Connect.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.717334 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::croak redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] usersCommon.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.835781 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::carp redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] usersCommon.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.835829 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::croak redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] usersCommon.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.835853 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::confess redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] Maintenance.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.844989 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Maintenance.pm: Useless use of concatenation (.) or string in void context at /imtonline/Common/Maintenance.pm line 637.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:41", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:41 2025] Authenticate.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq", "referer": null, "raw_line": "[Tue Aug 05 00:38:41.901227 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Authenticate.pm: Scalar value @foundType[0] better written as $foundType[0] at /imtonline/Common/Authenticate.pm line 157.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=********&load=ClientRedirect&do=inq"}, {"timestamp": "2025-08-05T00:38:40", "level": "cgi", "pid": 636072, "client_ip": "*************", "error_code": "AH01215", "message": "[Tu<PERSON> Aug  5 00:38:40 2025] MAJ_Account.pm", "script_path": "https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303", "referer": null, "raw_line": "[Tue Aug 05 00:38:40.943406 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:40 2025] MAJ_Account.pm: Smartmatch is experimental at /imtonline/Common/GAS/MAJ_Account.pm line 233.: /imtonline/Client/ClientEngine.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?load=ClientPView&id=8089303"}], "correlation_score": 79.**************, "correlation_method": "time_match(0.0s)+ip_match", "time_diff_seconds": 0.*****************}], "uncorrelated_500_errors": [], "correlation_timeline": {"2025-08-05 00:00": 1}, "top_error_patterns": {"other": 15}, "affected_scripts": {"ClientEngine.pl?load=ClientPView&id=8089303": 7, "ClientEngine.pl?version=********&load=ClientRedirect&do=inq": 8}}