#!/usr/bin/env python3
"""
Embed report data into HTML file
"""

import json

def embed_data_in_html():
    """Embed the report data directly into the HTML file"""
    
    # Load the report data
    with open('report_data.json', 'r') as f:
        report_data = json.load(f)
    
    # Read the HTML template
    with open('apache_log_report.html', 'r') as f:
        html_content = f.read()
    
    # Create the JavaScript data embedding
    data_js = f"""
        function loadEmbeddedData() {{
            reportData = {json.dumps(report_data, indent=2)};
            initializeReport();
        }}
    """
    
    # Replace the placeholder
    html_content = html_content.replace(
        '            // REPORT_DATA_PLACEHOLDER',
        data_js
    )
    
    # Write the final HTML file
    with open('apache_log_report.html', 'w') as f:
        f.write(html_content)
    
    print("Data embedded successfully into HTML report!")

if __name__ == "__main__":
    embed_data_in_html()
