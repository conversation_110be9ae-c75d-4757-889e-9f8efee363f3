#!/usr/bin/env python3
"""
Test script for log correlation functionality.

This script tests the correlation engine with sample data and validates
that it correctly identifies relationships between access log 500 errors
and error log entries.
"""

import unittest
import tempfile
import os
from datetime import datetime, timedelta
from log_correlation_engine import LogCorrelationEngine, AccessLogEntry, ErrorLogEntry


class TestLogCorrelation(unittest.TestCase):
    """Test cases for log correlation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.engine = LogCorrelationEngine(time_window_seconds=5)
        
        # Sample access log lines with 500 errors
        self.sample_access_logs = [
            '************* - - [05/Aug/2025:00:38:41 -0500] "GET /imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926 HTTP/1.1" 500 1551 63',
            '************** - - [04/Aug/2025:08:11:48 -0500] "POST /imtonline/ISO/WCOM_PPCBCEGTest.pl HTTP/1.1" 500 463 10',
            '************** - - [04/Aug/2025:08:09:39 -0500] "GET /imtonline/billing11/BillingEngine.pl?load=BillingAcctSummary HTTP/1.1" 500 532 607',
            '192.168.1.100 - - [05/Aug/2025:00:38:42 -0500] "GET /test.php HTTP/1.1" 200 1024 50'  # Not a 500 error
        ]
        
        # Sample error log lines
        self.sample_error_logs = [
            '[Tue Aug 05 00:38:41.717274 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] Connect.pm: Subroutine Common::Connect::carp redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq',
            '[Tue Aug 05 00:38:41.835781 2025] [cgi:error] [pid 636072] [client *************:55488] AH01215: [Tue Aug  5 00:38:41 2025] usersCommon.pm: Subroutine Common::Platform::Users::usersCommon::carp redefined at /usr/share/perl/5.30/Exporter.pm line 66.: /imtonline/WCom/WBOP.pl, referer: https://www.imtins.com/imtonline/Client/ClientEngine.pl?version=39930030&load=ClientRedirect&do=inq',
            '[Mon Aug 04 08:11:48.123456 2025] [cgi:error] [pid 123456] [client **************:12345] AH01215: [Mon Aug  4 08:11:48 2025] WCOM_PPCBCEGTest.pl: Database connection failed: /imtonline/ISO/WCOM_PPCBCEGTest.pl',
            '[Mon Aug 04 08:09:39.654321 2025] [cgi:error] [pid 789012] [client **************:54321] AH01215: [Mon Aug  4 08:09:39 2025] BillingEngine.pl: Timeout error in billing summary: /imtonline/billing11/BillingEngine.pl',
            '[Tue Aug 05 00:40:00.000000 2025] [cgi:error] [pid 999999] [client *************:12345] AH01215: [Tue Aug  5 00:40:00 2025] test.pl: Unrelated error: /imtonline/test.pl'
        ]
    
    def test_parse_access_log_line(self):
        """Test parsing of access log lines."""
        # Test 500 error parsing
        entry = self.engine.parse_access_log_line(self.sample_access_logs[0])
        self.assertIsNotNone(entry)
        self.assertEqual(entry.ip, '*************')
        self.assertEqual(entry.status, '500')
        self.assertEqual(entry.url, '/imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926')
        self.assertEqual(entry.response_time, 63)
        
        # Test non-500 error (should return None)
        entry = self.engine.parse_access_log_line(self.sample_access_logs[3])
        self.assertIsNone(entry)
    
    def test_parse_error_log_line(self):
        """Test parsing of error log lines."""
        entry = self.engine.parse_error_log_line(self.sample_error_logs[0])
        self.assertIsNotNone(entry)
        self.assertEqual(entry.client_ip, '*************')
        self.assertEqual(entry.pid, 636072)
        self.assertIn('Connect.pm', entry.message)
        self.assertEqual(entry.script_path, '/imtonline/WCom/WBOP.pl')
    
    def test_timestamp_parsing(self):
        """Test timestamp parsing for both log formats."""
        # Access log timestamp
        access_ts = self.engine.parse_timestamp('05/Aug/2025:00:38:41 -0500', 'access')
        self.assertIsNotNone(access_ts)
        self.assertEqual(access_ts.year, 2025)
        self.assertEqual(access_ts.month, 8)
        self.assertEqual(access_ts.day, 5)
        self.assertEqual(access_ts.hour, 0)
        self.assertEqual(access_ts.minute, 38)
        self.assertEqual(access_ts.second, 41)
        
        # Error log timestamp
        error_ts = self.engine.parse_timestamp('Tue Aug 05 00:38:41.717274 2025', 'error')
        self.assertIsNotNone(error_ts)
        self.assertEqual(error_ts.year, 2025)
        self.assertEqual(error_ts.month, 8)
        self.assertEqual(error_ts.day, 5)
        self.assertEqual(error_ts.hour, 0)
        self.assertEqual(error_ts.minute, 38)
        self.assertEqual(error_ts.second, 41)
    
    def test_correlation_scoring(self):
        """Test correlation scoring algorithm."""
        # Create test entries
        access_entry = AccessLogEntry(
            ip='*************',
            timestamp=datetime(2025, 8, 5, 0, 38, 41),
            method='GET',
            url='/imtonline/WCom/WBOP.pl?gid=4287232&package_id=534926',
            protocol='HTTP/1.1',
            status='500',
            size='1551',
            response_time=63,
            raw_line=self.sample_access_logs[0]
        )
        
        error_entry = ErrorLogEntry(
            timestamp=datetime(2025, 8, 5, 0, 38, 41),
            level='cgi',
            pid=636072,
            client_ip='*************',
            client_port=55488,
            error_code='AH01215',
            message='Connect.pm: Subroutine Common::Connect::carp redefined',
            script_path='/imtonline/WCom/WBOP.pl',
            referer='https://www.imtins.com/imtonline/Client/ClientEngine.pl',
            raw_line=self.sample_error_logs[0]
        )
        
        score, method = self.engine.calculate_correlation_score(access_entry, error_entry)
        
        # Should have high score due to exact time, IP, and script match
        self.assertGreater(score, 80)
        self.assertIn('time_match', method)
        self.assertIn('ip_match', method)
        self.assertIn('script_match', method)
    
    def test_correlation_with_time_window(self):
        """Test correlation within time window."""
        # Create entries with slight time difference
        access_entry = AccessLogEntry(
            ip='*************',
            timestamp=datetime(2025, 8, 5, 0, 38, 41),
            method='GET',
            url='/imtonline/WCom/WBOP.pl',
            protocol='HTTP/1.1',
            status='500',
            size='1551',
            response_time=63,
            raw_line=self.sample_access_logs[0]
        )
        
        # Error entry 2 seconds later
        error_entry = ErrorLogEntry(
            timestamp=datetime(2025, 8, 5, 0, 38, 43),
            level='cgi',
            pid=636072,
            client_ip='*************',
            client_port=55488,
            error_code='AH01215',
            message='Connect.pm: Subroutine Common::Connect::carp redefined',
            script_path='/imtonline/WCom/WBOP.pl',
            referer=None,
            raw_line=self.sample_error_logs[0]
        )
        
        score, method = self.engine.calculate_correlation_score(access_entry, error_entry)
        
        # Should still correlate within time window
        self.assertGreater(score, 50)
        self.assertIn('time_match', method)
        self.assertIn('ip_match', method)
    
    def test_full_correlation_process(self):
        """Test the complete correlation process."""
        # Parse all sample logs
        for line in self.sample_access_logs:
            entry = self.engine.parse_access_log_line(line)
            if entry:
                self.engine.access_500_errors.append(entry)
        
        for line in self.sample_error_logs:
            entry = self.engine.parse_error_log_line(line)
            if entry:
                self.engine.error_log_entries.append(entry)
        
        # Run correlation
        correlations = self.engine.correlate_logs()
        
        # Should find correlations
        self.assertGreater(len(correlations), 0)
        
        # Check statistics
        self.assertGreater(self.engine.stats['correlation_rate'], 0)
        self.assertGreater(self.engine.stats['successful_correlations'], 0)
    
    def test_correlation_report_generation(self):
        """Test correlation report generation."""
        # Add some test data
        for line in self.sample_access_logs[:2]:  # First 2 access logs
            entry = self.engine.parse_access_log_line(line)
            if entry:
                self.engine.access_500_errors.append(entry)
        
        for line in self.sample_error_logs[:3]:  # First 3 error logs
            entry = self.engine.parse_error_log_line(line)
            if entry:
                self.engine.error_log_entries.append(entry)
        
        # Run correlation
        self.engine.correlate_logs()
        
        # Generate report
        report = self.engine.get_correlation_report()
        
        # Validate report structure
        self.assertIn('summary', report)
        self.assertIn('correlations', report)
        self.assertIn('uncorrelated_500_errors', report)
        self.assertIn('top_error_patterns', report)
        self.assertIn('affected_scripts', report)
        
        # Check summary data
        self.assertEqual(report['summary']['total_500_errors'], len(self.engine.access_500_errors))
        self.assertEqual(report['summary']['total_error_log_entries'], len(self.engine.error_log_entries))


def run_integration_test():
    """Run integration test with actual log files."""
    print("\n" + "="*60)
    print("INTEGRATION TEST - CORRELATION ENGINE")
    print("="*60)
    
    # Initialize engine
    engine = LogCorrelationEngine(time_window_seconds=5)
    
    # Process actual log files
    access_files = ['access.log']
    error_files = ['www-error.log']
    
    for filepath in access_files:
        if os.path.exists(filepath):
            print(f"Processing {filepath}...")
            engine.process_access_log_file(filepath)
        else:
            print(f"Warning: {filepath} not found")
    
    for filepath in error_files:
        if os.path.exists(filepath):
            print(f"Processing {filepath}...")
            engine.process_error_log_file(filepath)
        else:
            print(f"Warning: {filepath} not found")
    
    if not engine.access_500_errors and not engine.error_log_entries:
        print("No log files found for integration test")
        return
    
    # Run correlation
    correlations = engine.correlate_logs()
    
    # Print results
    engine.print_summary()
    
    if correlations:
        print(f"\nFirst few correlations:")
        for i, correlation in enumerate(correlations[:3], 1):
            print(f"\n{i}. Score: {correlation.correlation_score:.1f}")
            print(f"   Access: {correlation.access_entry.timestamp} - {correlation.access_entry.ip}")
            print(f"   URL: {correlation.access_entry.url}")
            print(f"   Errors: {len(correlation.error_entries)} related error log entries")
    
    # Save test report
    engine.save_correlation_report('test_correlation_report.json')
    print(f"\nTest report saved to test_correlation_report.json")


if __name__ == "__main__":
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    run_integration_test()
