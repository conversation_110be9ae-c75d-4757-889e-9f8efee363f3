#!/usr/bin/env python3
"""
Log Correlation Engine for linking HTTP 500 errors from access logs 
to detailed error information in error logs.

This module provides functionality to correlate access log entries showing
HTTP 500 errors with corresponding detailed error messages, stack traces,
and diagnostic information from Apache error logs.
"""

import re
import gzip
import json
from datetime import datetime, timedelta
from collections import defaultdict, namedtuple
from typing import List, Dict, Optional, Tuple, Any
import statistics

# Data structures for log entries
AccessLogEntry = namedtuple('AccessLogEntry', [
    'ip', 'timestamp', 'method', 'url', 'protocol', 'status', 
    'size', 'response_time', 'raw_line'
])

ErrorLogEntry = namedtuple('ErrorLogEntry', [
    'timestamp', 'level', 'pid', 'client_ip', 'client_port', 
    'error_code', 'message', 'script_path', 'referer', 'raw_line'
])

CorrelatedError = namedtuple('CorrelatedError', [
    'access_entry', 'error_entries', 'correlation_score', 
    'correlation_method', 'time_diff_seconds'
])


class LogCorrelationEngine:
    """
    Engine for correlating HTTP 500 errors between access logs and error logs.
    
    Uses multiple correlation strategies:
    1. Timestamp + IP address matching (primary)
    2. Process ID matching when available
    3. URL/script path matching
    4. Time window correlation (±5 seconds default)
    """
    
    def __init__(self, time_window_seconds: int = 5):
        """
        Initialize the correlation engine.
        
        Args:
            time_window_seconds: Maximum time difference for correlation (default: 5)
        """
        self.time_window_seconds = time_window_seconds
        
        # Apache Common Log Format pattern
        self.access_log_pattern = re.compile(
            r'(\S+) - - \[([^\]]+)\] "(\S+) ([^"]*) (\S+)" (\d+) (\S+) (\d+)'
        )
        
        # Apache Error Log pattern - handles multiple formats
        self.error_log_patterns = [
            # Standard Apache error log format
            re.compile(
                r'\[([^\]]+)\] \[([^:]+):([^\]]+)\] \[pid (\d+)\] \[client ([^:]+):(\d+)\] '
                r'([^:]+): (.+?)(?:: ([^,]+))?(?:, referer: (.+))?$'
            ),
            # CGI error format
            re.compile(
                r'\[([^\]]+)\] \[([^:]+):([^\]]+)\] \[pid (\d+)\] \[client ([^:]+):(\d+)\] '
                r'([^:]+): \[([^\]]+)\] (.+?)(?:: ([^,]+))?(?:, referer: (.+))?$'
            ),
            # Simple error format
            re.compile(
                r'\[([^\]]+)\] \[([^:]+):([^\]]+)\] \[pid (\d+)\] (.+)$'
            )
        ]
        
        # Storage for parsed entries
        self.access_500_errors: List[AccessLogEntry] = []
        self.error_log_entries: List[ErrorLogEntry] = []
        self.correlations: List[CorrelatedError] = []
        
        # Statistics
        self.stats = {
            'total_500_errors': 0,
            'total_error_log_entries': 0,
            'successful_correlations': 0,
            'correlation_rate': 0.0,
            'avg_correlation_score': 0.0,
            'correlation_methods': defaultdict(int)
        }
    
    def parse_timestamp(self, timestamp_str: str, format_type: str = 'access') -> Optional[datetime]:
        """
        Parse timestamp from log entries.
        
        Args:
            timestamp_str: Timestamp string from log
            format_type: 'access' or 'error' log format
            
        Returns:
            Parsed datetime object or None if parsing fails
        """
        try:
            if format_type == 'access':
                # Apache access log format: 05/Aug/2025:00:38:41 -0500
                dt_part = timestamp_str.split()[0]
                return datetime.strptime(dt_part, '%d/%b/%Y:%H:%M:%S')
            else:
                # Apache error log format: Tue Aug 05 00:38:41.717274 2025
                # Handle microseconds if present
                if '.' in timestamp_str:
                    # Split on microseconds and year
                    parts = timestamp_str.split('.')
                    dt_part = parts[0]  # "Tue Aug 05 00:38:41"
                    year_part = parts[1].split()[-1]  # "2025"
                    dt_with_year = f"{dt_part} {year_part}"
                    return datetime.strptime(dt_with_year, '%a %b %d %H:%M:%S %Y')
                else:
                    return datetime.strptime(timestamp_str, '%a %b %d %H:%M:%S %Y')
        except ValueError as e:
            print(f"Warning: Could not parse timestamp '{timestamp_str}': {e}")
            return None
    
    def parse_access_log_line(self, line: str) -> Optional[AccessLogEntry]:
        """
        Parse a single access log line and extract 500 errors.
        
        Args:
            line: Raw log line
            
        Returns:
            AccessLogEntry if it's a 500 error, None otherwise
        """
        match = self.access_log_pattern.match(line.strip())
        if not match:
            return None
        
        ip, timestamp_str, method, url, protocol, status, size, response_time = match.groups()
        
        # Only process 500 errors
        if status != '500':
            return None
        
        timestamp = self.parse_timestamp(timestamp_str, 'access')
        if not timestamp:
            return None
        
        return AccessLogEntry(
            ip=ip,
            timestamp=timestamp,
            method=method,
            url=url,
            protocol=protocol,
            status=status,
            size=size,
            response_time=int(response_time) if response_time.isdigit() else 0,
            raw_line=line.strip()
        )
    
    def parse_error_log_line(self, line: str) -> Optional[ErrorLogEntry]:
        """
        Parse a single error log line.
        
        Args:
            line: Raw log line
            
        Returns:
            ErrorLogEntry or None if parsing fails
        """
        line = line.strip()
        if not line:
            return None
        
        # Try different error log patterns
        for pattern in self.error_log_patterns:
            match = pattern.match(line)
            if match:
                groups = match.groups()
                
                # Extract common fields
                timestamp_str = groups[0]
                timestamp = self.parse_timestamp(timestamp_str, 'error')
                if not timestamp:
                    continue
                
                if len(groups) >= 6:  # Standard format
                    level = groups[1]
                    error_type = groups[2]
                    pid = int(groups[3]) if groups[3].isdigit() else None
                    client_ip = groups[4] if len(groups) > 4 else None
                    client_port = int(groups[5]) if len(groups) > 5 and groups[5].isdigit() else None
                    error_code = groups[6] if len(groups) > 6 else None
                    message = groups[7] if len(groups) > 7 else ""
                    script_path = groups[9] if len(groups) > 9 else None
                    referer = groups[10] if len(groups) > 10 else None
                else:  # Simple format
                    level = groups[1] if len(groups) > 1 else "error"
                    error_type = groups[2] if len(groups) > 2 else ""
                    pid = int(groups[3]) if len(groups) > 3 and groups[3].isdigit() else None
                    client_ip = None
                    client_port = None
                    error_code = None
                    message = groups[4] if len(groups) > 4 else ""
                    script_path = None
                    referer = None
                
                return ErrorLogEntry(
                    timestamp=timestamp,
                    level=level,
                    pid=pid,
                    client_ip=client_ip,
                    client_port=client_port,
                    error_code=error_code,
                    message=message,
                    script_path=script_path,
                    referer=referer,
                    raw_line=line
                )
        
        return None

    def process_access_log_file(self, filepath: str) -> int:
        """
        Process an access log file and extract 500 errors.

        Args:
            filepath: Path to access log file

        Returns:
            Number of 500 errors found
        """
        print(f"Processing access log: {filepath}")

        opener = gzip.open if filepath.endswith('.gz') else open
        mode = 'rt' if filepath.endswith('.gz') else 'r'

        count = 0
        try:
            with opener(filepath, mode, encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    entry = self.parse_access_log_line(line)
                    if entry:
                        self.access_500_errors.append(entry)
                        count += 1

                    if line_num % 50000 == 0:
                        print(f"  Processed {line_num:,} lines, found {count} 500 errors...")
        except Exception as e:
            print(f"Error processing {filepath}: {e}")

        print(f"  Found {count} HTTP 500 errors")
        return count

    def process_error_log_file(self, filepath: str) -> int:
        """
        Process an error log file and extract error entries.

        Args:
            filepath: Path to error log file

        Returns:
            Number of error entries found
        """
        print(f"Processing error log: {filepath}")

        opener = gzip.open if filepath.endswith('.gz') else open
        mode = 'rt' if filepath.endswith('.gz') else 'r'

        count = 0
        try:
            with opener(filepath, mode, encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    entry = self.parse_error_log_line(line)
                    if entry:
                        self.error_log_entries.append(entry)
                        count += 1

                    if line_num % 50000 == 0:
                        print(f"  Processed {line_num:,} lines, found {count} error entries...")
        except Exception as e:
            print(f"Error processing {filepath}: {e}")

        print(f"  Found {count} error log entries")
        return count

    def calculate_correlation_score(self, access_entry: AccessLogEntry,
                                  error_entry: ErrorLogEntry) -> Tuple[float, str]:
        """
        Calculate correlation score between access log and error log entries.

        Args:
            access_entry: Access log entry
            error_entry: Error log entry

        Returns:
            Tuple of (score, method) where score is 0-100 and method describes correlation
        """
        score = 0.0
        methods = []

        # Time correlation (most important)
        time_diff = abs((access_entry.timestamp - error_entry.timestamp).total_seconds())
        if time_diff <= self.time_window_seconds:
            time_score = max(0, 50 - (time_diff * 10))  # 50 points max, decreasing with time
            score += time_score
            methods.append(f"time_match({time_diff:.1f}s)")

        # IP address correlation
        if error_entry.client_ip and access_entry.ip == error_entry.client_ip:
            score += 30
            methods.append("ip_match")

        # URL/Script path correlation
        if error_entry.script_path and access_entry.url:
            # Extract script name from URL and error log path
            url_script = access_entry.url.split('?')[0].split('/')[-1]
            error_script = error_entry.script_path.split('/')[-1]
            if url_script == error_script:
                score += 15
                methods.append("script_match")

        # Process ID correlation (if available)
        # Note: This is harder to correlate directly but can be used as supporting evidence

        return score, "+".join(methods) if methods else "no_match"

    def correlate_logs(self) -> List[CorrelatedError]:
        """
        Correlate 500 errors from access logs with error log entries.

        Returns:
            List of correlated errors
        """
        print(f"Correlating {len(self.access_500_errors)} 500 errors with {len(self.error_log_entries)} error entries...")

        correlations = []

        for access_entry in self.access_500_errors:
            best_matches = []

            # Find error log entries within time window
            for error_entry in self.error_log_entries:
                time_diff = abs((access_entry.timestamp - error_entry.timestamp).total_seconds())

                if time_diff <= self.time_window_seconds:
                    score, method = self.calculate_correlation_score(access_entry, error_entry)

                    if score > 0:
                        best_matches.append((error_entry, score, method, time_diff))

            # Sort by score and take the best matches
            best_matches.sort(key=lambda x: x[1], reverse=True)

            if best_matches:
                # Take all matches with score >= 50 (good correlation)
                good_matches = [match for match in best_matches if match[1] >= 50]

                if good_matches:
                    error_entries = [match[0] for match in good_matches]
                    avg_score = sum(match[1] for match in good_matches) / len(good_matches)
                    primary_method = good_matches[0][2]
                    avg_time_diff = sum(match[3] for match in good_matches) / len(good_matches)

                    correlation = CorrelatedError(
                        access_entry=access_entry,
                        error_entries=error_entries,
                        correlation_score=avg_score,
                        correlation_method=primary_method,
                        time_diff_seconds=avg_time_diff
                    )
                    correlations.append(correlation)

        self.correlations = correlations
        self._update_statistics()

        print(f"Successfully correlated {len(correlations)} out of {len(self.access_500_errors)} 500 errors")
        return correlations

    def _update_statistics(self):
        """Update correlation statistics."""
        self.stats['total_500_errors'] = len(self.access_500_errors)
        self.stats['total_error_log_entries'] = len(self.error_log_entries)
        self.stats['successful_correlations'] = len(self.correlations)

        if self.stats['total_500_errors'] > 0:
            self.stats['correlation_rate'] = (
                self.stats['successful_correlations'] / self.stats['total_500_errors'] * 100
            )

        if self.correlations:
            self.stats['avg_correlation_score'] = statistics.mean(
                [c.correlation_score for c in self.correlations]
            )

            # Count correlation methods
            for correlation in self.correlations:
                self.stats['correlation_methods'][correlation.correlation_method] += 1

    def get_correlation_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive correlation report.

        Returns:
            Dictionary containing correlation analysis results
        """
        report = {
            'summary': dict(self.stats),
            'correlations': [],
            'uncorrelated_500_errors': [],
            'correlation_timeline': defaultdict(int),
            'top_error_patterns': defaultdict(int),
            'affected_scripts': defaultdict(int)
        }

        # Convert correlations to serializable format
        for correlation in self.correlations:
            correlation_data = {
                'access_log': {
                    'timestamp': correlation.access_entry.timestamp.isoformat(),
                    'ip': correlation.access_entry.ip,
                    'method': correlation.access_entry.method,
                    'url': correlation.access_entry.url,
                    'response_time': correlation.access_entry.response_time,
                    'raw_line': correlation.access_entry.raw_line
                },
                'error_logs': [],
                'correlation_score': correlation.correlation_score,
                'correlation_method': correlation.correlation_method,
                'time_diff_seconds': correlation.time_diff_seconds
            }

            for error_entry in correlation.error_entries:
                error_data = {
                    'timestamp': error_entry.timestamp.isoformat(),
                    'level': error_entry.level,
                    'pid': error_entry.pid,
                    'client_ip': error_entry.client_ip,
                    'error_code': error_entry.error_code,
                    'message': error_entry.message,
                    'script_path': error_entry.script_path,
                    'referer': error_entry.referer,
                    'raw_line': error_entry.raw_line
                }
                correlation_data['error_logs'].append(error_data)

                # Update pattern tracking
                if error_entry.script_path:
                    script_name = error_entry.script_path.split('/')[-1]
                    report['affected_scripts'][script_name] += 1

                # Extract error patterns
                if error_entry.message:
                    # Simple pattern extraction - look for common error types
                    message_lower = error_entry.message.lower()
                    if 'uninitialized' in message_lower:
                        report['top_error_patterns']['uninitialized_variable'] += 1
                    elif 'undefined' in message_lower:
                        report['top_error_patterns']['undefined_variable'] += 1
                    elif 'redefined' in message_lower:
                        report['top_error_patterns']['redefined_subroutine'] += 1
                    elif 'prototype' in message_lower:
                        report['top_error_patterns']['prototype_mismatch'] += 1
                    else:
                        report['top_error_patterns']['other'] += 1

            report['correlations'].append(correlation_data)

            # Timeline tracking
            hour_key = correlation.access_entry.timestamp.strftime('%Y-%m-%d %H:00')
            report['correlation_timeline'][hour_key] += 1

        # Find uncorrelated 500 errors
        correlated_access_entries = {c.access_entry for c in self.correlations}
        for access_entry in self.access_500_errors:
            if access_entry not in correlated_access_entries:
                uncorrelated_data = {
                    'timestamp': access_entry.timestamp.isoformat(),
                    'ip': access_entry.ip,
                    'method': access_entry.method,
                    'url': access_entry.url,
                    'response_time': access_entry.response_time,
                    'raw_line': access_entry.raw_line
                }
                report['uncorrelated_500_errors'].append(uncorrelated_data)

        return report

    def save_correlation_report(self, filepath: str):
        """
        Save correlation report to JSON file.

        Args:
            filepath: Output file path
        """
        report = self.get_correlation_report()

        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        print(f"Correlation report saved to {filepath}")

    def print_summary(self):
        """Print a summary of correlation results."""
        print("\n" + "="*60)
        print("LOG CORRELATION SUMMARY")
        print("="*60)
        print(f"Total HTTP 500 errors found: {self.stats['total_500_errors']}")
        print(f"Total error log entries processed: {self.stats['total_error_log_entries']}")
        print(f"Successfully correlated: {self.stats['successful_correlations']}")
        print(f"Correlation rate: {self.stats['correlation_rate']:.1f}%")

        if self.correlations:
            print(f"Average correlation score: {self.stats['avg_correlation_score']:.1f}")
            print("\nCorrelation methods used:")
            for method, count in self.stats['correlation_methods'].items():
                print(f"  {method}: {count}")

        print("\nTop correlated errors:")
        for i, correlation in enumerate(self.correlations[:5], 1):
            print(f"\n{i}. {correlation.access_entry.timestamp} - {correlation.access_entry.ip}")
            print(f"   URL: {correlation.access_entry.url}")
            print(f"   Score: {correlation.correlation_score:.1f} ({correlation.correlation_method})")
            print(f"   Error entries: {len(correlation.error_entries)}")
            if correlation.error_entries:
                print(f"   Primary error: {correlation.error_entries[0].message[:100]}...")


def main():
    """Example usage of the correlation engine."""
    import glob
    import sys

    if len(sys.argv) < 2:
        print("Usage: python log_correlation_engine.py <access_log_pattern> [error_log_pattern]")
        print("Example: python log_correlation_engine.py 'access.log*' 'www-error.log*'")
        sys.exit(1)

    access_pattern = sys.argv[1]
    error_pattern = sys.argv[2] if len(sys.argv) > 2 else 'www-error.log*'

    # Initialize correlation engine
    engine = LogCorrelationEngine(time_window_seconds=5)

    # Process access logs
    access_files = glob.glob(access_pattern)
    if not access_files:
        print(f"No access log files found matching: {access_pattern}")
        sys.exit(1)

    for filepath in sorted(access_files):
        engine.process_access_log_file(filepath)

    # Process error logs
    error_files = glob.glob(error_pattern)
    if not error_files:
        print(f"No error log files found matching: {error_pattern}")
        sys.exit(1)

    for filepath in sorted(error_files):
        engine.process_error_log_file(filepath)

    # Perform correlation
    correlations = engine.correlate_logs()

    # Print summary
    engine.print_summary()

    # Save detailed report
    engine.save_correlation_report('log_correlation_report.json')


if __name__ == "__main__":
    main()
