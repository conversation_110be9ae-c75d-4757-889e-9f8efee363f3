# Log Correlation System

This system provides comprehensive correlation between HTTP 500 errors recorded in access logs and their corresponding detailed error entries in error logs. It enables tracking a 500 error from the access log entry back to specific error details, stack traces, and diagnostic information.

## Features

- **Multi-strategy Correlation**: Uses timestamp, IP address, process ID, and script path matching
- **Configurable Time Windows**: Adjustable correlation time window (default ±5 seconds)
- **Comprehensive Reporting**: JSON and HTML report generation with detailed correlation analysis
- **Integration Ready**: Seamlessly integrates with existing log analysis pipeline
- **Command Line Interface**: Standalone script for flexible correlation analysis

## Architecture

### Core Components

1. **LogCorrelationEngine** (`log_correlation_engine.py`)
   - Main correlation engine with parsing and matching logic
   - Supports Apache Common Log Format and Apache Error Log formats
   - Implements scoring algorithm for correlation confidence

2. **Command Line Interface** (`correlate_logs.py`)
   - Standalone script for running correlation analysis
   - Supports multiple output formats and filtering options
   - Batch processing of multiple log files

3. **Integration Module** (enhanced `generate_report_data.py`)
   - Integrates correlation analysis into existing reporting pipeline
   - Adds correlation data to comprehensive log analysis reports

## Correlation Algorithm

The system uses a multi-factor scoring algorithm to correlate access log 500 errors with error log entries:

### Scoring Factors

1. **Time Correlation (0-50 points)**
   - Exact timestamp match: 50 points
   - Within time window: 50 - (time_diff * 10) points
   - Outside window: 0 points

2. **IP Address Match (30 points)**
   - Exact client IP match between access and error logs

3. **Script/URL Match (15 points)**
   - Matching script name extracted from URL and error log path

4. **Process ID Match (future enhancement)**
   - Correlation based on Apache process ID when available

### Correlation Confidence Levels

- **High Confidence (80+ points)**: Strong correlation with multiple matching factors
- **Medium Confidence (60-79 points)**: Good correlation with some matching factors
- **Low Confidence (50-59 points)**: Weak correlation, primarily time-based

## Installation and Setup

### Prerequisites

- Python 3.6+
- Access to Apache access logs and error logs
- Required Python modules: `re`, `gzip`, `json`, `datetime`, `collections`, `statistics`

### Files

```
log_correlation_engine.py    # Core correlation engine
correlate_logs.py           # Command line interface
test_correlation.py         # Unit tests and integration tests
generate_report_data.py     # Enhanced reporting (modified)
CORRELATION_README.md       # This documentation
```

## Usage

### Basic Correlation Analysis

```bash
# Correlate current access and error logs
python correlate_logs.py access.log* www-error.log*

# Specify time window and output format
python correlate_logs.py --time-window 10 --output-format html access.log error.log

# Filter by date range
python correlate_logs.py --start-date 2025-08-05 --end-date 2025-08-06 access.log* error.log*
```

### Advanced Options

```bash
# Set minimum correlation score
python correlate_logs.py --min-score 70 access.log* error.log*

# Custom output file
python correlate_logs.py --output correlation_analysis.json access.log* error.log*

# Verbose output
python correlate_logs.py --verbose access.log* error.log*
```

### Integration with Existing Pipeline

The correlation analysis is automatically integrated when running the enhanced report generator:

```bash
python generate_report_data.py
```

This will include correlation data in the `report_data.json` output.

## API Usage

### Basic Correlation

```python
from log_correlation_engine import LogCorrelationEngine

# Initialize engine
engine = LogCorrelationEngine(time_window_seconds=5)

# Process log files
engine.process_access_log_file('access.log')
engine.process_error_log_file('www-error.log')

# Run correlation
correlations = engine.correlate_logs()

# Get detailed report
report = engine.get_correlation_report()

# Print summary
engine.print_summary()
```

### Custom Filtering

```python
# Filter correlations by score
high_confidence = [c for c in correlations if c.correlation_score >= 80]

# Filter by IP address
specific_ip_correlations = [
    c for c in correlations 
    if c.access_entry.ip == '*************'
]

# Filter by time range
from datetime import datetime
start_time = datetime(2025, 8, 5, 0, 0, 0)
end_time = datetime(2025, 8, 5, 23, 59, 59)

time_filtered = [
    c for c in correlations 
    if start_time <= c.access_entry.timestamp <= end_time
]
```

## Output Formats

### JSON Report Structure

```json
{
  "summary": {
    "total_500_errors": 150,
    "total_error_log_entries": 5000,
    "successful_correlations": 120,
    "correlation_rate": 80.0,
    "avg_correlation_score": 75.5
  },
  "correlations": [
    {
      "access_log": {
        "timestamp": "2025-08-05T00:38:41",
        "ip": "*************",
        "method": "GET",
        "url": "/imtonline/WCom/WBOP.pl?gid=4287232",
        "response_time": 63
      },
      "error_logs": [
        {
          "timestamp": "2025-08-05T00:38:41",
          "level": "cgi",
          "pid": 636072,
          "client_ip": "*************",
          "message": "Connect.pm: Subroutine redefined error",
          "script_path": "/imtonline/WCom/WBOP.pl"
        }
      ],
      "correlation_score": 95.0,
      "correlation_method": "time_match+ip_match+script_match"
    }
  ],
  "uncorrelated_500_errors": [...],
  "top_error_patterns": {...},
  "affected_scripts": {...}
}
```

### HTML Report

The HTML report provides a visual interface with:
- Summary statistics dashboard
- Detailed correlation listings with expandable error details
- Color-coded correlation confidence levels
- Uncorrelated 500 errors section
- Interactive elements for exploring correlations

## Testing

### Unit Tests

```bash
python test_correlation.py
```

### Integration Test

The integration test processes actual log files if available:

```bash
python test_correlation.py
```

### Manual Testing

```bash
# Test with sample data
python correlate_logs.py access.log www-error.log --verbose

# Verify correlation accuracy
python correlate_logs.py --min-score 80 --output-format html access.log* error.log*
```

## Performance Considerations

### Memory Usage

- Large log files are processed line by line to minimize memory usage
- Correlation data is stored in memory during analysis
- Consider processing log files in batches for very large datasets

### Processing Time

- Correlation time scales with the product of 500 errors and error log entries
- Time window size affects correlation performance
- Typical processing: ~1000 correlations per second

### Optimization Tips

1. **Reduce Time Window**: Smaller windows improve performance
2. **Filter by Date**: Process only relevant time ranges
3. **Batch Processing**: Process log files in chronological batches
4. **Minimum Score**: Use higher minimum scores to reduce output size

## Troubleshooting

### Common Issues

1. **No Correlations Found**
   - Check time synchronization between access and error logs
   - Verify log file formats match expected patterns
   - Increase time window if clocks are not synchronized

2. **Low Correlation Rates**
   - Check if error logs contain client IP information
   - Verify script path extraction is working correctly
   - Consider adjusting correlation scoring weights

3. **Performance Issues**
   - Reduce time window size
   - Process smaller date ranges
   - Filter logs before correlation

### Debug Mode

Enable verbose output for debugging:

```bash
python correlate_logs.py --verbose access.log error.log
```

## Future Enhancements

1. **Enhanced Process ID Correlation**: Better PID tracking across log entries
2. **Machine Learning Scoring**: ML-based correlation confidence scoring
3. **Real-time Correlation**: Live correlation for streaming log data
4. **Custom Log Formats**: Support for additional log formats
5. **Distributed Processing**: Support for processing across multiple servers

## Contributing

When contributing to the correlation system:

1. Maintain backward compatibility with existing log formats
2. Add unit tests for new correlation strategies
3. Update documentation for new features
4. Consider performance impact of changes
5. Test with real-world log data

## License

This correlation system is part of the access log analysis toolkit and follows the same licensing terms as the parent project.
