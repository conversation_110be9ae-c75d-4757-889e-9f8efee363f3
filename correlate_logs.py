#!/usr/bin/env python3
"""
Command-line interface for log correlation analysis.

This script provides a comprehensive interface for correlating HTTP 500 errors
from access logs with detailed error information from error logs.
"""

import argparse
import glob
import sys
import os
from datetime import datetime, timedelta
from log_correlation_engine import LogCorrelationEngine


def parse_date(date_str):
    """Parse date string in various formats."""
    formats = ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%m/%d/%Y', '%d/%b/%Y']
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    raise ValueError(f"Unable to parse date: {date_str}")


def filter_logs_by_date(engine, start_date=None, end_date=None):
    """Filter log entries by date range."""
    if not start_date and not end_date:
        return
    
    print(f"Filtering logs by date range: {start_date} to {end_date}")
    
    # Filter access log entries
    if start_date or end_date:
        original_count = len(engine.access_500_errors)
        engine.access_500_errors = [
            entry for entry in engine.access_500_errors
            if (not start_date or entry.timestamp >= start_date) and
               (not end_date or entry.timestamp <= end_date)
        ]
        filtered_count = len(engine.access_500_errors)
        print(f"  Access logs: {original_count} -> {filtered_count} entries")
    
    # Filter error log entries
    if start_date or end_date:
        original_count = len(engine.error_log_entries)
        engine.error_log_entries = [
            entry for entry in engine.error_log_entries
            if (not start_date or entry.timestamp >= start_date) and
               (not end_date or entry.timestamp <= end_date)
        ]
        filtered_count = len(engine.error_log_entries)
        print(f"  Error logs: {original_count} -> {filtered_count} entries")


def generate_html_report(engine, output_file):
    """Generate an HTML report with correlation results."""
    report_data = engine.get_correlation_report()
    
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Log Correlation Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; justify-content: space-around; margin: 20px 0; }}
        .stat-box {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        .correlation {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .correlation.high-score {{ border-left: 5px solid #28a745; }}
        .correlation.medium-score {{ border-left: 5px solid #ffc107; }}
        .correlation.low-score {{ border-left: 5px solid #dc3545; }}
        .error-details {{ background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }}
        .timestamp {{ color: #666; font-size: 0.9em; }}
        .url {{ font-family: monospace; background-color: #f1f1f1; padding: 2px 4px; }}
        .error-message {{ font-family: monospace; font-size: 0.9em; color: #d63384; }}
        pre {{ background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Log Correlation Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <div class="stat-box">
            <h3>{report_data['summary']['total_500_errors']}</h3>
            <p>Total 500 Errors</p>
        </div>
        <div class="stat-box">
            <h3>{report_data['summary']['successful_correlations']}</h3>
            <p>Correlated Errors</p>
        </div>
        <div class="stat-box">
            <h3>{report_data['summary']['correlation_rate']:.1f}%</h3>
            <p>Correlation Rate</p>
        </div>
        <div class="stat-box">
            <h3>{report_data['summary']['avg_correlation_score']:.1f}</h3>
            <p>Avg Score</p>
        </div>
    </div>
    
    <h2>Correlated Errors</h2>
"""
    
    for i, correlation in enumerate(report_data['correlations'], 1):
        score = correlation['correlation_score']
        score_class = 'high-score' if score >= 80 else 'medium-score' if score >= 60 else 'low-score'
        
        html_content += f"""
    <div class="correlation {score_class}">
        <h3>Correlation #{i} (Score: {score:.1f})</h3>
        <div class="timestamp">Access Log: {correlation['access_log']['timestamp']}</div>
        <p><strong>IP:</strong> {correlation['access_log']['ip']}</p>
        <p><strong>URL:</strong> <span class="url">{correlation['access_log']['url']}</span></p>
        <p><strong>Method:</strong> {correlation['access_log']['method']}</p>
        <p><strong>Response Time:</strong> {correlation['access_log']['response_time']}ms</p>
        <p><strong>Correlation Method:</strong> {correlation['correlation_method']}</p>
        
        <h4>Related Error Log Entries ({len(correlation['error_logs'])})</h4>
"""
        
        for j, error in enumerate(correlation['error_logs'], 1):
            html_content += f"""
        <div class="error-details">
            <div class="timestamp">Error #{j}: {error['timestamp']}</div>
            <p><strong>Level:</strong> {error['level']}</p>
            <p><strong>PID:</strong> {error['pid']}</p>
            <p><strong>Script:</strong> {error['script_path'] or 'N/A'}</p>
            <p><strong>Error:</strong></p>
            <div class="error-message">{error['message']}</div>
            <details>
                <summary>Raw Log Line</summary>
                <pre>{error['raw_line']}</pre>
            </details>
        </div>
"""
        
        html_content += """
    </div>
"""
    
    # Add uncorrelated errors section
    if report_data['uncorrelated_500_errors']:
        html_content += f"""
    <h2>Uncorrelated 500 Errors ({len(report_data['uncorrelated_500_errors'])})</h2>
    <p>These 500 errors could not be correlated with error log entries:</p>
"""
        
        for error in report_data['uncorrelated_500_errors'][:10]:  # Show first 10
            html_content += f"""
    <div class="correlation">
        <div class="timestamp">{error['timestamp']}</div>
        <p><strong>IP:</strong> {error['ip']}</p>
        <p><strong>URL:</strong> <span class="url">{error['url']}</span></p>
        <p><strong>Method:</strong> {error['method']}</p>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    with open(output_file, 'w') as f:
        f.write(html_content)
    
    print(f"HTML report saved to {output_file}")


def main():
    parser = argparse.ArgumentParser(
        description='Correlate HTTP 500 errors from access logs with error log details',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s access.log* www-error.log*
  %(prog)s --time-window 10 --start-date 2025-08-05 access.log error.log
  %(prog)s --output-format html --output correlation_report.html access.log* error.log*
        """
    )
    
    parser.add_argument('access_logs',
                       help='Access log files or patterns (e.g., "access.log*")')
    parser.add_argument('error_logs',
                       help='Error log files or patterns (e.g., "www-error.log*")')
    
    parser.add_argument('--time-window', type=int, default=5,
                       help='Time window for correlation in seconds (default: 5)')
    parser.add_argument('--start-date', type=str,
                       help='Start date for filtering (YYYY-MM-DD or YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--end-date', type=str,
                       help='End date for filtering (YYYY-MM-DD or YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--output-format', choices=['json', 'html'], default='json',
                       help='Output format (default: json)')
    parser.add_argument('--output', '-o', type=str,
                       help='Output file (default: log_correlation_report.json/html)')
    parser.add_argument('--min-score', type=float, default=50.0,
                       help='Minimum correlation score to include (default: 50.0)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--correlation-method', choices=['auto', 'time-indexed', 'binary-search', 'original'],
                       default='auto', help='Correlation algorithm to use (default: auto)')
    parser.add_argument('--max-error-entries', type=int,
                       help='Limit number of error entries processed (for testing)')
    
    args = parser.parse_args()
    
    # Parse dates
    start_date = None
    end_date = None
    
    if args.start_date:
        try:
            start_date = parse_date(args.start_date)
        except ValueError as e:
            print(f"Error parsing start date: {e}")
            sys.exit(1)
    
    if args.end_date:
        try:
            end_date = parse_date(args.end_date)
        except ValueError as e:
            print(f"Error parsing end date: {e}")
            sys.exit(1)
    
    # Expand file patterns
    access_files = glob.glob(args.access_logs)
    if not access_files:
        print(f"Warning: No files found matching access log pattern: {args.access_logs}")

    error_files = glob.glob(args.error_logs)
    if not error_files:
        print(f"Warning: No files found matching error log pattern: {args.error_logs}")
    
    if not access_files:
        print("Error: No access log files found")
        sys.exit(1)
    
    if not error_files:
        print("Error: No error log files found")
        sys.exit(1)
    
    # Initialize correlation engine
    engine = LogCorrelationEngine(time_window_seconds=args.time_window)
    
    # Process files
    print(f"Processing {len(access_files)} access log files...")
    for filepath in sorted(access_files):
        engine.process_access_log_file(filepath)
    
    print(f"Processing {len(error_files)} error log files...")
    for filepath in sorted(error_files):
        engine.process_error_log_file(filepath)
    
    # Apply date filtering
    filter_logs_by_date(engine, start_date, end_date)

    # Limit error entries if specified (for testing)
    if args.max_error_entries and len(engine.error_log_entries) > args.max_error_entries:
        print(f"Limiting error entries to {args.max_error_entries} for testing...")
        engine.error_log_entries = engine.error_log_entries[:args.max_error_entries]

    # Perform correlation using specified method
    if args.correlation_method == 'auto':
        correlations = engine.correlate_logs()
    elif args.correlation_method == 'time-indexed':
        correlations = engine.correlate_logs_time_indexed()
    elif args.correlation_method == 'binary-search':
        correlations = engine.correlate_logs_binary_search()
    elif args.correlation_method == 'original':
        correlations = engine.correlate_logs_original()
    else:
        correlations = engine.correlate_logs()
    
    # Filter by minimum score
    if args.min_score > 0:
        original_count = len(engine.correlations)
        engine.correlations = [c for c in engine.correlations if c.correlation_score >= args.min_score]
        filtered_count = len(engine.correlations)
        print(f"Filtered correlations by score >= {args.min_score}: {original_count} -> {filtered_count}")
    
    # Print summary
    engine.print_summary()
    
    # Generate output
    if args.output_format == 'html':
        output_file = args.output or 'log_correlation_report.html'
        generate_html_report(engine, output_file)
    else:
        output_file = args.output or 'log_correlation_report.json'
        engine.save_correlation_report(output_file)


if __name__ == "__main__":
    main()
