#!/usr/bin/env python3
"""
Apache Access Log Analyzer
Analyzes Apache access logs and generates comprehensive HTML reports
"""

import re
import gzip
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import ipaddress
from urllib.parse import urlparse, parse_qs
import statistics

class ApacheLogAnalyzer:
    def __init__(self):
        # Apache Common Log Format with additional fields
        # IP - - [timestamp] "method url protocol" status size response_time
        self.log_pattern = re.compile(
            r'(\S+) - - \[([^\]]+)\] "(\S+) ([^"]*) (\S+)" (\d+) (\S+) (\d+)'
        )
        
        self.data = {
            'total_requests': 0,
            'unique_ips': set(),
            'status_codes': Counter(),
            'methods': Counter(),
            'urls': Counter(),
            'ips': Counter(),
            'hourly_traffic': defaultdict(int),
            'daily_traffic': defaultdict(int),
            'response_sizes': [],
            'response_times': [],
            'user_agents': Counter(),
            'suspicious_ips': [],
            'error_patterns': [],
            'bot_traffic': [],
            'large_requests': [],
            'failed_auth': [],
            'date_range': {'start': None, 'end': None}
        }
        
        # Suspicious patterns
        self.suspicious_patterns = {
            'sql_injection': re.compile(r'(union|select|insert|delete|drop|script|alert)', re.IGNORECASE),
            'path_traversal': re.compile(r'\.\.\/'),
            'xss_attempt': re.compile(r'(<script|javascript:|onload=)', re.IGNORECASE)
        }
        
    def parse_timestamp(self, timestamp_str):
        """Parse Apache timestamp format"""
        try:
            # Format: 30/Jul/2025:00:00:15 -0500
            dt = datetime.strptime(timestamp_str.split()[0], '%d/%b/%Y:%H:%M:%S')
            return dt
        except ValueError:
            return None
    
    def is_suspicious_ip(self, ip, request_count):
        """Identify suspicious IP addresses"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # High request volume threshold
            if request_count > 1000:
                return True
                
            # Check for non-private IPs with unusual patterns
            if not ip_obj.is_private and request_count > 100:
                return True
                
            return False
        except ValueError:
            return False
    
    def analyze_url_patterns(self, url):
        """Analyze URLs for suspicious patterns"""
        suspicious_indicators = []
        
        for pattern_name, pattern in self.suspicious_patterns.items():
            if pattern.search(url):
                suspicious_indicators.append(pattern_name)
        
        return suspicious_indicators
    
    def process_log_line(self, line):
        """Process a single log line"""
        match = self.log_pattern.match(line.strip())
        if not match:
            return
        
        ip, timestamp_str, method, url, protocol, status, size, response_time = match.groups()
        
        # Parse timestamp
        timestamp = self.parse_timestamp(timestamp_str)
        if not timestamp:
            return
        
        # Update date range
        if not self.data['date_range']['start'] or timestamp < self.data['date_range']['start']:
            self.data['date_range']['start'] = timestamp
        if not self.data['date_range']['end'] or timestamp > self.data['date_range']['end']:
            self.data['date_range']['end'] = timestamp
        
        # Basic metrics
        self.data['total_requests'] += 1
        self.data['unique_ips'].add(ip)
        self.data['status_codes'][status] += 1
        self.data['methods'][method] += 1
        self.data['urls'][url] += 1
        self.data['ips'][ip] += 1
        
        # Time-based analysis
        hour_key = timestamp.strftime('%Y-%m-%d %H:00')
        day_key = timestamp.strftime('%Y-%m-%d')
        self.data['hourly_traffic'][hour_key] += 1
        self.data['daily_traffic'][day_key] += 1
        
        # Response size and time
        try:
            if size != '-':
                self.data['response_sizes'].append(int(size))
            self.data['response_times'].append(int(response_time))
        except ValueError:
            pass
        
        # Security analysis
        suspicious_patterns = self.analyze_url_patterns(url)
        if suspicious_patterns:
            self.data['error_patterns'].append({
                'ip': ip,
                'url': url,
                'timestamp': timestamp.isoformat(),
                'patterns': suspicious_patterns,
                'status': status
            })
        
        # Failed authentication (401, 403)
        if status in ['401', '403']:
            self.data['failed_auth'].append({
                'ip': ip,
                'url': url,
                'timestamp': timestamp.isoformat(),
                'status': status
            })
        
        # Large requests (potential DoS)
        try:
            if size != '-' and int(size) > 1000000:  # > 1MB
                self.data['large_requests'].append({
                    'ip': ip,
                    'url': url,
                    'size': int(size),
                    'timestamp': timestamp.isoformat()
                })
        except ValueError:
            pass
        
        # Bot detection (robots.txt requests)
        if 'robots.txt' in url or 'bot' in url.lower():
            self.data['bot_traffic'].append({
                'ip': ip,
                'url': url,
                'timestamp': timestamp.isoformat()
            })
    
    def process_file(self, filepath):
        """Process a single log file"""
        print(f"Processing {filepath}...")
        
        if filepath.endswith('.gz'):
            opener = gzip.open
            mode = 'rt'
        else:
            opener = open
            mode = 'r'
        
        try:
            with opener(filepath, mode, encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    self.process_log_line(line)
                    if line_num % 50000 == 0:
                        print(f"  Processed {line_num:,} lines...")
        except Exception as e:
            print(f"Error processing {filepath}: {e}")
    
    def analyze_all_logs(self):
        """Process all log files in the current directory"""
        log_files = []
        
        # Get all log files
        for filename in os.listdir('.'):
            if filename.startswith('access.log'):
                log_files.append(filename)
        
        # Sort files to process in chronological order
        log_files.sort()
        
        for log_file in log_files:
            self.process_file(log_file)
        
        # Post-processing analysis
        self.identify_suspicious_ips()
        self.calculate_statistics()
    
    def identify_suspicious_ips(self):
        """Identify suspicious IP addresses based on request patterns"""
        for ip, count in self.data['ips'].most_common(50):
            if self.is_suspicious_ip(ip, count):
                # Get status code distribution for this IP
                ip_status_codes = Counter()
                ip_urls = Counter()
                
                # This is a simplified approach - in a real implementation,
                # we'd need to track per-IP statistics during parsing
                self.data['suspicious_ips'].append({
                    'ip': ip,
                    'request_count': count,
                    'reason': 'High request volume'
                })
    
    def calculate_statistics(self):
        """Calculate summary statistics"""
        self.data['stats'] = {
            'total_requests': self.data['total_requests'],
            'unique_ips': len(self.data['unique_ips']),
            'avg_requests_per_ip': self.data['total_requests'] / len(self.data['unique_ips']) if self.data['unique_ips'] else 0,
            'avg_response_size': statistics.mean(self.data['response_sizes']) if self.data['response_sizes'] else 0,
            'avg_response_time': statistics.mean(self.data['response_times']) if self.data['response_times'] else 0,
            'error_rate': sum(1 for status in self.data['status_codes'] if status.startswith(('4', '5'))) / self.data['total_requests'] * 100,
            'top_status_codes': dict(self.data['status_codes'].most_common(10)),
            'top_ips': dict(self.data['ips'].most_common(10)),
            'top_urls': dict(self.data['urls'].most_common(10)),
            'peak_hour': max(self.data['hourly_traffic'].items(), key=lambda x: x[1]) if self.data['hourly_traffic'] else None,
            'peak_day': max(self.data['daily_traffic'].items(), key=lambda x: x[1]) if self.data['daily_traffic'] else None
        }

if __name__ == "__main__":
    analyzer = ApacheLogAnalyzer()
    analyzer.analyze_all_logs()
    
    # Save analysis results
    # Convert sets to lists for JSON serialization
    analyzer.data['unique_ips'] = list(analyzer.data['unique_ips'])
    
    # Convert Counter objects to dicts
    for key in ['status_codes', 'methods', 'urls', 'ips', 'user_agents']:
        analyzer.data[key] = dict(analyzer.data[key])
    
    # Convert datetime objects to strings
    if analyzer.data['date_range']['start']:
        analyzer.data['date_range']['start'] = analyzer.data['date_range']['start'].isoformat()
    if analyzer.data['date_range']['end']:
        analyzer.data['date_range']['end'] = analyzer.data['date_range']['end'].isoformat()
    
    with open('log_analysis.json', 'w') as f:
        json.dump(analyzer.data, f, indent=2, default=str)
    
    print(f"\nAnalysis complete!")
    print(f"Total requests: {analyzer.data['total_requests']:,}")
    print(f"Unique IPs: {len(analyzer.data['unique_ips']):,}")
    print(f"Date range: {analyzer.data['date_range']['start']} to {analyzer.data['date_range']['end']}")
    print(f"Results saved to log_analysis.json")
